"""
AkShare函数调用工具

提供调用akshare函数并返回JSON格式结果的纯函数实现。
"""

import json
import logging
from typing import Any

import akshare as ak
import pandas as pd

logger = logging.getLogger(__name__)


def call_akshare_function(function_name: str, **kwargs) -> dict[str, Any]:
    """调用akshare函数并返回JSON格式结果
    
    Args:
        function_name: akshare函数名称
        **kwargs: 函数参数
        
    Returns:
        包含执行结果的字典，格式为：
        {
            "success": bool,
            "function_name": str,
            "data": Any,  # 转换为JSON兼容格式的数据
            "data_type": str,  # "dataframe", "list", "dict", "other"
            "error": str | None,  # 错误信息（如果有）
            "row_count": int | None,  # DataFrame行数（如果适用）
            "column_count": int | None  # DataFrame列数（如果适用）
        }
    """
    try:
        # 检查函数是否存在
        if not hasattr(ak, function_name):
            return {
                "success": False,
                "function_name": function_name,
                "data": None,
                "data_type": "error",
                "error": f"函数 '{function_name}' 不存在于akshare模块中",
                "row_count": None,
                "column_count": None
            }
        
        # 获取函数并调用
        func = getattr(ak, function_name)
        logger.debug(f"调用akshare函数: {function_name}, 参数: {kwargs}")
        
        result = func(**kwargs)
        
        # 处理不同类型的返回结果
        return _process_akshare_result(function_name, result)
        
    except Exception as e:
        logger.error(f"调用akshare函数 {function_name} 失败: {e}")
        return {
            "success": False,
            "function_name": function_name,
            "data": None,
            "data_type": "error",
            "error": str(e),
            "row_count": None,
            "column_count": None
        }


def _process_akshare_result(function_name: str, result: Any) -> dict[str, Any]:
    """处理akshare函数的返回结果
    
    Args:
        function_name: 函数名称
        result: akshare函数返回的结果
        
    Returns:
        处理后的结果字典
    """
    try:
        if result is None:
            return {
                "success": True,
                "function_name": function_name,
                "data": None,
                "data_type": "none",
                "error": None,
                "row_count": None,
                "column_count": None
            }
        
        elif isinstance(result, pd.DataFrame):
            # DataFrame转换为JSON兼容格式
            if result.empty:
                data = []
                row_count = 0
                column_count = len(result.columns)
            else:
                # 将DataFrame转换为records格式的字典列表
                data = result.to_dict('records')
                row_count = len(result)
                column_count = len(result.columns)
            
            return {
                "success": True,
                "function_name": function_name,
                "data": data,
                "data_type": "dataframe",
                "error": None,
                "row_count": row_count,
                "column_count": column_count
            }
        
        elif isinstance(result, list):
            return {
                "success": True,
                "function_name": function_name,
                "data": result,
                "data_type": "list",
                "error": None,
                "row_count": len(result) if result else 0,
                "column_count": None
            }
        
        elif isinstance(result, dict):
            return {
                "success": True,
                "function_name": function_name,
                "data": result,
                "data_type": "dict",
                "error": None,
                "row_count": None,
                "column_count": None
            }
        
        else:
            # 其他类型，尝试转换为JSON兼容格式
            try:
                json_data = json.loads(json.dumps(result, default=str))
                return {
                    "success": True,
                    "function_name": function_name,
                    "data": json_data,
                    "data_type": "other",
                    "error": None,
                    "row_count": None,
                    "column_count": None
                }
            except (TypeError, ValueError):
                # 无法序列化，转换为字符串
                return {
                    "success": True,
                    "function_name": function_name,
                    "data": str(result),
                    "data_type": "string",
                    "error": None,
                    "row_count": None,
                    "column_count": None
                }
                
    except Exception as e:
        logger.error(f"处理akshare函数 {function_name} 结果失败: {e}")
        return {
            "success": False,
            "function_name": function_name,
            "data": None,
            "data_type": "error",
            "error": f"结果处理失败: {str(e)}",
            "row_count": None,
            "column_count": None
        }
