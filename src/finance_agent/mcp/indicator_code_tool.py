"""
指标编程实现提示词工具

根据akshare API的JSON定义、结果数据和指标设计，生成用于指标编程实现的提示词。
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any

import pandas as pd
from jinja2 import Environment, FileSystemLoader

logger = logging.getLogger(__name__)


def generate_indicator_code_prompt(
    api_name: str, 
    category: str,
    data_dir: str = "data/akshare",
    prompt_template_path: str = "data/prompt/indicator_code_prompt.jinja2"
) -> dict[str, Any]:
    """生成指标编程实现提示词
    
    Args:
        api_name: API名称
        category: 数据分类
        data_dir: 数据目录路径
        prompt_template_path: 提示词模板路径

    Returns:
        包含提示词生成结果的字典，格式为：
        {
            "success": bool,
            "api_name": str,
            "category": str,
            "prompt": str,  # 生成的提示词
            "has_result_data": bool,
            "has_indicators": bool,
            "data_summary": dict,  # 数据摘要信息
            "error": str | None
        }
    """
    # 读取API定义
    api_json_path = Path(data_dir) / category / f"{api_name}.json"
    try:
        with open(api_json_path, 'r', encoding='utf-8') as f:
            api_definition = json.load(f)
    except FileNotFoundError:
        return {
            "success": False,
            "api_name": api_name,
            "category": category,
            "prompt": "",
            "has_result_data": False,
            "has_indicators": False,
            "data_summary": {},
            "error": f"API定义文件不存在: {api_json_path}"
        }
    except Exception as e:
        return {
            "success": False,
            "api_name": api_name,
            "category": category,
            "prompt": "",
            "has_result_data": False,
            "has_indicators": False,
            "data_summary": {},
            "error": f"读取API定义文件失败: {e}"
        }
        
    # 读取结果数据
    result_csv_path = Path(data_dir) / category / f"{api_name}_result.csv"
    has_result_data = False
    data_info = {}

    if result_csv_path.exists():
        try:
            result_data = pd.read_csv(result_csv_path)
            has_result_data = True
            data_info = _analyze_result_data(result_data)
            logger.info(f"成功读取结果数据: {len(result_data)} 行 × {len(result_data.columns)} 列")
        except Exception as e:
            logger.warning(f"读取结果数据失败: {e}")
            # 继续执行，不返回错误

    # 从文件系统读取指标设计结果
    design_file_path = Path("src/finance_agent/function") / category / api_name / "indicator_design.md"
    try:
        with open(design_file_path, 'r', encoding='utf-8') as f:
            design_content = f.read()
        processed_indicators = _parse_design_file(design_content)
        has_indicators = len(processed_indicators) > 0
        logger.info(f"从文件读取到指标设计结果: {len(processed_indicators)} 个分析师类型")
    except FileNotFoundError:
        return {
            "success": False,
            "api_name": api_name,
            "category": category,
            "prompt": "",
            "has_result_data": has_result_data,
            "has_indicators": False,
            "data_summary": data_info,
            "error": f"指标设计文件不存在: {design_file_path}"
        }
    except Exception as e:
        return {
            "success": False,
            "api_name": api_name,
            "category": category,
            "prompt": "",
            "has_result_data": has_result_data,
            "has_indicators": False,
            "data_summary": data_info,
            "error": f"读取指标设计文件失败: {e}"
        }

    # 准备模板变量
    template_vars = {
        "api_name": api_name,
        "category": category,
        "target_date": datetime.now().strftime("%Y-%m-%d"),
        "api_definition": api_definition,
        "has_result_data": has_result_data,
        "data_info": data_info,
        "indicators": processed_indicators,
        "has_indicators": has_indicators
    }

    # 加载并渲染模板
    template_path = Path(prompt_template_path)
    try:
        env = Environment(loader=FileSystemLoader(template_path.parent))
        template = env.get_template(template_path.name)
        prompt = template.render(**template_vars)
    except FileNotFoundError:
        return {
            "success": False,
            "api_name": api_name,
            "category": category,
            "prompt": "",
            "has_result_data": has_result_data,
            "has_indicators": has_indicators,
            "data_summary": data_info,
            "error": f"提示词模板文件不存在: {prompt_template_path}"
        }
    except Exception as e:
        return {
            "success": False,
            "api_name": api_name,
            "category": category,
            "prompt": "",
            "has_result_data": has_result_data,
            "has_indicators": has_indicators,
            "data_summary": data_info,
            "error": f"模板渲染失败: {e}"
        }

    return {
        "success": True,
        "api_name": api_name,
        "category": category,
        "prompt": prompt,
        "has_result_data": has_result_data,
        "has_indicators": has_indicators,
        "data_summary": data_info,
        "error": None
    }


def _analyze_result_data(result_data: pd.DataFrame) -> dict[str, Any]:
    """分析结果数据，直接展示真实数据给LLM
    
    Args:
        result_data: 结果数据DataFrame
        
    Returns:
        数据分析结果字典
    """
    try:
        # 基本信息
        row_count, column_count = result_data.shape

        # 如果数据过长，只取前20行
        sample_data = result_data.head(20) if row_count > 20 else result_data
        sample_rows = len(sample_data)

        # 直接将数据转换为字符串展示
        data_sample = sample_data.to_string(max_rows=20, max_cols=None, line_width=None)

        # 列信息分析
        columns_info = []
        numeric_columns = 0
        text_columns = 0
        date_columns = 0

        for col in result_data.columns:
            # 获取样本值（非空的前3个值）
            non_null_values = result_data[col].dropna()
            sample_values = non_null_values.head(3).astype(str).tolist() if len(non_null_values) > 0 else []
            
            columns_info.append({
                "name": str(col),
                "dtype": str(result_data[col].dtype),
                "sample_values": sample_values
            })

            # 统计列类型
            if pd.api.types.is_numeric_dtype(result_data[col]):
                numeric_columns += 1
            elif pd.api.types.is_datetime64_any_dtype(result_data[col]):
                date_columns += 1
            else:
                text_columns += 1

        # 数据完整性计算
        total_cells = row_count * column_count
        missing_cells = result_data.isnull().sum().sum()
        completeness = round((total_cells - missing_cells) / total_cells * 100, 2) if total_cells > 0 else 0
        missing_columns = (result_data.isnull().sum() > 0).sum()

        return {
            "row_count": row_count,
            "column_count": column_count,
            "sample_rows": sample_rows,
            "data_sample": data_sample,
            "columns": columns_info,
            "completeness": completeness,
            "missing_values": missing_columns,
            "numeric_columns": numeric_columns,
            "text_columns": text_columns,
            "date_columns": date_columns
        }

    except Exception as e:
        logger.error(f"数据分析失败: {e}")
        return {
            "row_count": 0,
            "column_count": 0,
            "sample_rows": 0,
            "data_sample": "数据分析失败",
            "columns": [],
            "completeness": 0,
            "missing_values": 0,
            "numeric_columns": 0,
            "text_columns": 0,
            "date_columns": 0
        }

def _parse_design_file(content: str) -> dict[str, list[dict]]:
    """解析指标设计文件内容

    Args:
        content: 指标设计文件的内容

    Returns:
        解析后的指标字典
    """
    import re

    indicators = {}

    # 查找分析师指标部分
    analyst_pattern = r'### (.+?)指标\s*\n(.*?)(?=###|$)'
    analyst_matches = re.findall(analyst_pattern, content, re.DOTALL)

    for analyst_name, analyst_content in analyst_matches:
        analyst_name = analyst_name.strip()

        # 查找指标
        indicator_pattern = r'\d+\.\s*\*\*(.+?)\*\*\s*\n\s*-\s*计算公式：(.+?)\n\s*-\s*业务含义：(.+?)\n\s*-\s*数值范围：(.+?)(?=\n\d+\.\s*\*\*|\n###|\n##|$)'
        indicator_matches = re.findall(indicator_pattern, analyst_content, re.DOTALL)

        analyst_indicators = []
        for name, formula, meaning, value_range in indicator_matches:
            analyst_indicators.append({
                "name": name.strip(),
                "formula": formula.strip(),
                "business_meaning": meaning.strip(),
                "value_range": value_range.strip()
            })

        if analyst_indicators:
            indicators[analyst_name] = analyst_indicators

    return indicators
