"""
AkShare MCP服务器

基于FastMCP框架的MCP服务器，提供akshare API工具服务。
支持DataCategory枚举参数初始化和自动缓存激活。
"""

import logging
from typing import Any

from mcp.server.fastmcp import FastMCP

from ..akshare import AkShareCache
from ..base.enums import DataCategory
from .akshare_function_tool import call_akshare_function
from .indicator_design_tool import generate_indicator_design_prompt
from .indicator_code_tool import generate_indicator_code_prompt

logger = logging.getLogger(__name__)


class AkShareMCPServer:
    """AkShare MCP服务器包装器
    
    基于FastMCP框架的MCP服务器，提供akshare API工具服务。
    使用组合模式包装FastMCP实例，支持DataCategory枚举参数初始化。
    """

    def __init__(self, data_category: DataCategory):
        """初始化AkShare MCP服务器
        
        Args:
            data_category: 数据分类枚举，用于配置服务器名称和功能范围
        """
        self.data_category = data_category

        # 创建FastMCP实例
        server_name = f"AkShare-{data_category.value.title()}-Server"
        self.mcp = FastMCP(server_name)

        # 缓存管理器
        self.cache_manager: AkShareCache | None = None

        # 初始化API列表和指针
        self.api_list = self._load_api_list()
        self.current_api_index = None  # 当前API指针，初始为None

        # 激活akshare缓存
        self._setup_akshare_cache()

        # 注册MCP工具
        self._setup_tools()
        
        logger.info(f"AkShare MCP服务器已初始化: {server_name}")

    def _load_api_list(self) -> list[str]:
        """加载API列表

        Returns:
            API名称列表
        """
        from pathlib import Path

        data_dir = Path("data/akshare") / self.data_category.value
        if not data_dir.exists():
            logger.warning(f"数据目录不存在: {data_dir}")
            return []

        api_list = []
        for json_file in data_dir.glob("*.json"):
            # 排除_result.csv对应的文件
            if not json_file.name.endswith("_result.json"):
                api_name = json_file.stem
                api_list.append(api_name)

        api_list.sort()  # 按字母顺序排序
        logger.info(f"加载了 {len(api_list)} 个API: {self.data_category.value}")
        return api_list

    def _setup_akshare_cache(self) -> None:
        """激活akshare缓存以提高工具调用效率"""
        try:
            logger.info("正在激活AkShare API缓存...")
            self.cache_manager = AkShareCache()
            result = self.cache_manager.apply_cache_to_all_functions()
            
            functions_cached = result.get('functions_cached', 0)
            logger.info(f"AkShare缓存激活成功，已为 {functions_cached} 个函数应用缓存")
            
        except Exception as e:
            logger.warning(f"AkShare缓存激活失败: {e}")
            logger.info("将继续运行，但性能可能受到影响")

    def _setup_tools(self) -> None:
        """设置MCP工具"""
        # 注册akshare函数调用工具
        @self.mcp.tool(
            name="call_akshare_api",
            description="调用AkShare金融数据API获取实时金融数据"
        )
        def call_akshare_api(function_name: str, **kwargs) -> dict[str, Any]:
            """调用AkShare金融数据API获取实时金融数据

            支持调用所有AkShare提供的金融数据API，包括股票、期货、债券、基金、
            宏观经济、行业数据等各类金融数据接口。

            Args:
                function_name (str): AkShare API函数名称
                    示例: "stock_zh_a_hist", "fund_etf_hist_em", "macro_china_gdp"
                **kwargs: API函数的具体参数，根据不同API而异
                    常用参数示例:
                    - symbol (str): 股票代码，如 "000001" 或 "000001.SZ"
                    - period (str): 数据周期，如 "daily", "weekly", "monthly"
                    - start_date (str): 开始日期，格式 "20240101"
                    - end_date (str): 结束日期，格式 "20241231"
                    - adjust (str): 复权类型，如 "qfq"(前复权), "hfq"(后复权)

            Returns:
                dict: 包含调用结果的字典，格式为：
                {
                    "success": bool,           # 调用是否成功
                    "function_name": str,      # 调用的函数名称
                    "data": Any,              # 转换为JSON兼容格式的数据
                    "data_type": str,         # "dataframe", "list", "dict", "other"
                    "error": str | None,      # 错误信息（如果有）
                    "row_count": int | None,  # DataFrame行数（如果适用）
                    "column_count": int | None # DataFrame列数（如果适用）
                }

            Examples:
                获取股票历史数据:
                call_akshare_function(
                    function_name="stock_zh_a_hist",
                    symbol="000001",
                    period="daily",
                    start_date="20240101",
                    end_date="20241231",
                    adjust="qfq"
                )

                获取基金ETF数据:
                call_akshare_function(
                    function_name="fund_etf_hist_em",
                    symbol="159919",
                    period="daily",
                    start_date="20240101",
                    end_date="20241231",
                    adjust="qfq"
                )
            """
            return call_akshare_function(function_name, **kwargs)

        # 注册指标设计提示词工具
        @self.mcp.tool(
            name="generate_indicator_design",
            description="生成指标设计提示词，用于为AkShare API设计投资分析指标"
        )
        def generate_indicator_design() -> dict[str, Any]:
            """生成指标设计提示词

            根据当前指针状态选择API：
            - 如果指针为None，选择第一个API
            - 如果指针不为None，移动到下一个API

            Returns:
                包含提示词生成结果的字典
            """
            # 根据指针状态选择API
            if self.current_api_index is None:
                # 第一次调用，选择第一个API
                self.current_api_index = 0
                logger.info("首次调用设计工具，选择第一个API")
            else:
                # 后续调用，移动到下一个API
                self.current_api_index += 1
                logger.info(f"移动到下一个API，索引: {self.current_api_index}")

            # 检查是否还有API
            if self.current_api_index >= len(self.api_list):
                return {
                    "success": False,
                    "api_name": "",
                    "category": self.data_category.value,
                    "prompt": "",
                    "has_result_data": False,
                    "data_summary": {},
                    "error": "所有API都已处理完成"
                }

            # 获取当前API
            api_name = self.api_list[self.current_api_index]

            # 调用指标设计工具
            result = generate_indicator_design_prompt(
                api_name=api_name,
                category=self.data_category.value,
                data_dir="data/akshare",
                prompt_template_path="data/prompt/indicator_design_prompt.jinja2"
            )

            # 在结果中添加状态信息
            result["current_api_index"] = self.current_api_index
            result["current_api"] = api_name
            result["total_apis"] = len(self.api_list)
            result["remaining_apis"] = len(self.api_list) - self.current_api_index - 1

            return result

        # 注册指标编程实现提示词工具
        @self.mcp.tool(
            name="generate_indicator_code",
            description="生成指标编程实现提示词，用于将已设计的指标转换为Python代码"
        )
        def generate_indicator_code() -> dict[str, Any]:
            """生成指标编程实现提示词

            直接使用当前指针指向的API进行编程实现。
            调用此工具表示设计成功，需要进行编程实现。

            Returns:
                包含提示词生成结果的字典
            """
            # 检查是否有当前API
            if self.current_api_index is None or self.current_api_index >= len(self.api_list):
                return {
                    "success": False,
                    "api_name": "",
                    "category": self.data_category.value,
                    "prompt": "",
                    "has_result_data": False,
                    "has_indicators": False,
                    "data_summary": {},
                    "error": "没有可用的API进行编程实现"
                }

            # 直接使用当前指针指向的API
            api_name = self.api_list[self.current_api_index]
            logger.info(f"使用当前API进行编程实现: {api_name}")

            # 调用指标编程工具
            result = generate_indicator_code_prompt(
                api_name=api_name,
                category=self.data_category.value,
                data_dir="data/akshare",
                prompt_template_path="data/prompt/indicator_code_prompt.jinja2"
            )

            # 在结果中添加状态信息
            result["current_api_index"] = self.current_api_index
            result["current_api"] = api_name
            result["total_apis"] = len(self.api_list)
            result["remaining_apis"] = len(self.api_list) - self.current_api_index - 1

            return result

    def run(self, **kwargs) -> None:
        """运行MCP服务器
        
        Args:
            **kwargs: 传递给FastMCP.run()的参数
        """
        logger.info(f"启动AkShare MCP服务器: {self.data_category.value}")
        self.mcp.run(**kwargs)

    def get_cache_stats(self) -> dict[str, Any] | None:
        """获取缓存统计信息
        
        Returns:
            缓存统计信息字典，如果缓存未启用则返回None
        """
        if self.cache_manager:
            return self.cache_manager.get_cache_stats()
        return None

    @property
    def fastmcp_instance(self) -> FastMCP:
        """获取内部的FastMCP实例
        
        Returns:
            FastMCP实例
        """
        return self.mcp