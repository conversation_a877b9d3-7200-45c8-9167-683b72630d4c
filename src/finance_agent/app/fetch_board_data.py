"""
板块数据抓取脚本
用于执行概念板块和行业板块数据的抓取工作
"""

import argparse
import sys

from ..akshare import BoardDataFetcher, AkShareCache


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="抓取板块数据")
    parser.add_argument(
        "--output-dir",
        type=str,
        default="data/akshare",
        help="数据保存目录，默认data/akshare",
    )
    parser.add_argument(
        "--type",
        choices=["concept", "industry", "all"],
        default="all",
        help="抓取数据类型: concept(概念板块), industry(行业板块), all(全部)",
    )
    parser.add_argument(
        "--delay", type=float, default=0.5, help="请求间隔时间（秒），默认0.5秒"
    )
    parser.add_argument(
        "--skip-existing", action="store_true", help="跳过已经抓取的板块，支持断点续传"
    )
    parser.add_argument(
        "--enable-cache", action="store_true", help="启用AkShare API缓存以提升性能"
    )

    args = parser.parse_args()

    # 初始化AkShare缓存（如果启用）
    if args.enable_cache:
        print("🚀 启用AkShare API缓存...")
        cache = AkShareCache()
        result = cache.apply_cache_to_all_functions()
        print(f"✅ 已为 {result['functions_cached']} 个函数应用缓存")

    # 创建数据抓取器
    fetcher = BoardDataFetcher(data_dir=args.output_dir)

    try:
        if args.type == "concept":
            # 只抓取概念板块数据
            mode_text = "断点续传模式" if args.skip_existing else "全量抓取模式"
            print(f"开始抓取概念板块数据... ({mode_text})")
            concept_file = fetcher.fetch_all_concept_data(
                delay=args.delay, skip_existing=args.skip_existing
            )
            if concept_file:
                print(f"概念板块数据已保存到: {concept_file}")
            else:
                print("概念板块数据抓取失败")
                sys.exit(1)

        elif args.type == "industry":
            # 只抓取行业板块数据
            mode_text = "断点续传模式" if args.skip_existing else "全量抓取模式"
            print(f"开始抓取行业板块数据... ({mode_text})")
            industry_file = fetcher.fetch_all_industry_data(
                delay=args.delay, skip_existing=args.skip_existing
            )
            if industry_file:
                print(f"行业板块数据已保存到: {industry_file}")
            else:
                print("行业板块数据抓取失败")
                sys.exit(1)

        else:  # args.type == "all"
            # 抓取所有板块数据
            mode_text = "断点续传模式" if args.skip_existing else "全量抓取模式"
            print(f"开始抓取所有板块数据... ({mode_text})")
            result = fetcher.fetch_all_board_data(
                delay=args.delay, skip_existing=args.skip_existing
            )

            if result:
                print("\n抓取完成！")
                if "concept" in result:
                    print(f"概念板块数据已保存到: {result['concept']}")
                if "industry" in result:
                    print(f"行业板块数据已保存到: {result['industry']}")
            else:
                print("板块数据抓取失败")
                sys.exit(1)

    except KeyboardInterrupt:
        print("\n用户中断，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"抓取过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
