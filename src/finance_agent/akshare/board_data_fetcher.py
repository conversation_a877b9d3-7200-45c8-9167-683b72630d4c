"""
板块数据抓取器
用于获取概念板块和行业板块的成份股数据
"""

from pathlib import Path
import json
from datetime import datetime
import time

import pandas as pd
import akshare as ak


class BoardDataFetcher:
    """板块数据抓取器，支持概念板块和行业板块"""

    def __init__(self, data_dir: str):
        """
        初始化板块数据抓取器

        Args:
            data_dir: 数据保存目录
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)

    @staticmethod
    def get_concept_boards(max_retries: int = 3) -> list[str]:
        """
        获取所有概念板块名称

        Args:
            max_retries: 最大重试次数

        Returns:
            概念板块名称列表
        """
        for attempt in range(max_retries):
            try:
                concept_boards_df = ak.stock_board_concept_name_em()
                return concept_boards_df["板块名称"].tolist()
            except (ConnectionError, TimeoutError, ValueError, KeyError) as e:
                print(f"获取概念板块列表失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2**attempt)  # 指数退避
                else:
                    print(f"获取概念板块列表最终失败: {e}")
        return []

    @staticmethod
    def get_industry_boards(max_retries: int = 3) -> list[str]:
        """
        获取所有行业板块名称

        Args:
            max_retries: 最大重试次数

        Returns:
            行业板块名称列表
        """
        for attempt in range(max_retries):
            try:
                industry_boards_df = ak.stock_board_industry_name_em()
                return industry_boards_df["板块名称"].tolist()
            except Exception as e:
                print(f"获取行业板块列表失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2**attempt)  # 指数退避
                else:
                    print(f"获取行业板块列表最终失败: {e}")
        return []

    @staticmethod
    def _convert_stock_code(stock_code: str) -> str:
        """
        转换股票代码为8位格式

        Args:
            stock_code: 6位股票代码

        Returns:
            8位股票代码 (如 SZ000001)
        """
        if stock_code.startswith("0") or stock_code.startswith("3"):
            return f"SZ{stock_code}"
        elif stock_code.startswith("6") or stock_code.startswith("9"):
            return f"SH{stock_code}"
        elif stock_code.startswith("8"):
            return f"BJ{stock_code}"
        else:
            return f"SZ{stock_code}"  # 默认深圳

    @staticmethod
    def _load_existing_data(file_path: Path) -> dict:
        """
        加载已存在的数据文件

        Args:
            file_path: 数据文件路径

        Returns:
            已存在的数据字典，如果文件不存在则返回空字典
        """
        if file_path.exists():
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                print(f"读取已存在数据文件失败: {e}")
        return {}

    @staticmethod
    def _save_data_to_file(
            file_path: Path, data: dict, data_type: str, success_count: int
    ):
        """
        保存数据到文件

        Args:
            file_path: 文件路径
            data: 要保存的数据
            data_type: 数据类型 ('concepts' 或 'industries')
            success_count: 成功获取的数量
        """
        save_data = {
            "update_time": datetime.now().isoformat(),
            "success_count": success_count,
            "data": data,
        }

        if data_type == "concepts":
            save_data["total_concepts"] = len(data)
        else:
            save_data["total_industries"] = len(data)

        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

    def fetch_concept_stocks(
        self, concept_name: str, max_retries: int = 2
    ) -> list[dict] | None:
        """
        获取指定概念板块的成份股

        Args:
            concept_name: 概念板块名称
            max_retries: 最大重试次数

        Returns:
            成份股信息列表，失败时返回None
        """
        for attempt in range(max_retries):
            try:
                concept_stocks_df = ak.stock_board_concept_cons_em(symbol=concept_name)
                if concept_stocks_df.empty:
                    return []
                return self._extract_stock_info(concept_stocks_df)
            except (ConnectionError, TimeoutError, ValueError, KeyError) as e:
                if attempt < max_retries - 1:
                    print(
                        f"获取概念板块 {concept_name} 成份股失败 (尝试 {attempt + 1}/{max_retries}): {e}"
                    )
                    time.sleep(1)
                else:
                    print(f"获取概念板块 {concept_name} 成份股最终失败: {e}")
        return None

    def fetch_industry_stocks(
        self, industry_name: str, max_retries: int = 2
    ) -> list[dict] | None:
        """
        获取指定行业板块的成份股

        Args:
            industry_name: 行业板块名称
            max_retries: 最大重试次数

        Returns:
            成份股信息列表，失败时返回None
        """
        for attempt in range(max_retries):
            try:
                industry_stocks_df = ak.stock_board_industry_cons_em(
                    symbol=industry_name
                )
                if industry_stocks_df.empty:
                    return []
                return self._extract_stock_info(industry_stocks_df)
            except (ConnectionError, TimeoutError, ValueError, KeyError) as e:
                if attempt < max_retries - 1:
                    print(
                        f"获取行业板块 {industry_name} 成份股失败 (尝试 {attempt + 1}/{max_retries}): {e}"
                    )
                    time.sleep(1)
                else:
                    print(f"获取行业板块 {industry_name} 成份股最终失败: {e}")
        return None

    def fetch_all_concept_data(
        self, delay: float = 0.5, skip_existing: bool = False
    ) -> str:
        """
        获取所有概念板块的成份股数据并保存

        Args:
            delay: 请求间隔时间（秒）
            skip_existing: 是否跳过已经抓取的板块

        Returns:
            保存文件的路径
        """
        print("开始获取概念板块数据...")

        # 创建保存目录
        concept_dir = self.data_dir / "concept"
        concept_dir.mkdir(parents=True, exist_ok=True)

        # 输出文件路径
        output_file = concept_dir / "concept_stocks.json"

        # 加载已存在的数据
        existing_data = {}
        if skip_existing:
            existing_data = self._load_existing_data(output_file)
            if existing_data and "data" in existing_data:
                print(f"加载已存在的数据，包含 {len(existing_data['data'])} 个概念板块")

        # 获取所有概念板块
        concept_boards = self.get_concept_boards()
        if not concept_boards:
            print("未能获取概念板块列表")
            return ""

        print(f"找到 {len(concept_boards)} 个概念板块")

        # 存储所有数据的字典
        all_concept_data = existing_data.get("data", {}) if skip_existing else {}
        success_count = len(all_concept_data) if skip_existing else 0

        # 过滤掉已经抓取的板块
        if skip_existing and all_concept_data:
            remaining_boards = [
                board for board in concept_boards if board not in all_concept_data
            ]
            skipped_count = len(concept_boards) - len(remaining_boards)
            print(
                f"跳过已抓取的 {skipped_count} 个概念板块，剩余 {len(remaining_boards)} 个"
            )
            concept_boards = remaining_boards

        # 遍历每个概念板块
        total_boards = len(concept_boards)
        if total_boards == 0:
            print("没有需要抓取的概念板块")
        else:
            for i, concept_name in enumerate(concept_boards, 1):
                print(f"正在获取 {i}/{total_boards}: {concept_name}")

                stocks_info = self.fetch_concept_stocks(concept_name)

                if stocks_info is not None:
                    all_concept_data[concept_name] = stocks_info
                    success_count += 1
                    print(f"  获取到 {len(stocks_info)} 只股票")

                    # 每获取10个板块就保存一次，避免数据丢失
                    if i % 10 == 0:
                        self._save_data_to_file(
                            output_file, all_concept_data, "concepts", success_count
                        )
                        print(f"  已保存进度: {i}/{total_boards}")
                else:
                    print(f"  {concept_name} 获取失败")

                # 添加延迟避免请求过快
                if delay > 0:
                    time.sleep(delay)
        # 最终保存数据到JSON文件
        self._save_data_to_file(
            output_file, all_concept_data, "concepts", success_count
        )

        print(f"\n概念板块数据已保存到: {output_file}")
        print(f"成功获取了 {success_count}/{len(concept_boards)} 个概念板块的数据")

        # 统计信息
        total_stocks = sum(len(stocks) for stocks in all_concept_data.values())
        print(f"总计 {total_stocks} 条股票记录")

        return str(output_file)

    def fetch_all_industry_data(
        self, delay: float = 0.1, skip_existing: bool = False
    ) -> str:
        """
        获取所有行业板块的成份股数据并保存

        Args:
            delay: 请求间隔时间（秒）
            skip_existing: 是否跳过已经抓取的板块

        Returns:
            保存文件的路径
        """
        print("开始获取行业板块数据...")

        # 创建保存目录
        industry_dir = self.data_dir / "industry"
        industry_dir.mkdir(parents=True, exist_ok=True)

        # 输出文件路径
        output_file = industry_dir / "industry_stocks.json"

        # 加载已存在的数据
        existing_data = {}
        if skip_existing:
            existing_data = self._load_existing_data(output_file)
            if existing_data and "data" in existing_data:
                print(f"加载已存在的数据，包含 {len(existing_data['data'])} 个行业板块")

        # 获取所有行业板块
        industry_boards = self.get_industry_boards()
        if not industry_boards:
            print("未能获取行业板块列表")
            return ""

        print(f"找到 {len(industry_boards)} 个行业板块")

        # 存储所有数据的字典
        all_industry_data = existing_data.get("data", {}) if skip_existing else {}
        success_count = len(all_industry_data) if skip_existing else 0

        # 过滤掉已经抓取的板块
        if skip_existing and all_industry_data:
            remaining_boards = [
                board for board in industry_boards if board not in all_industry_data
            ]
            skipped_count = len(industry_boards) - len(remaining_boards)
            print(
                f"跳过已抓取的 {skipped_count} 个行业板块，剩余 {len(remaining_boards)} 个"
            )
            industry_boards = remaining_boards

        # 遍历每个行业板块
        total_boards = len(industry_boards)
        if total_boards == 0:
            print("没有需要抓取的行业板块")
        else:
            for i, industry_name in enumerate(industry_boards, 1):
                print(f"正在获取 {i}/{total_boards}: {industry_name}")

                stocks_info = self.fetch_industry_stocks(industry_name)

                if stocks_info is not None:
                    all_industry_data[industry_name] = stocks_info
                    success_count += 1
                    print(f"  获取到 {len(stocks_info)} 只股票")

                    # 每获取10个板块就保存一次，避免数据丢失
                    if i % 10 == 0:
                        self._save_data_to_file(
                            output_file, all_industry_data, "industries", success_count
                        )
                        print(f"  已保存进度: {i}/{total_boards}")
                else:
                    print(f"  {industry_name} 获取失败")

                # 添加延迟避免请求过快
                if delay > 0:
                    time.sleep(delay)

        # 最终保存数据到JSON文件
        self._save_data_to_file(
            output_file, all_industry_data, "industries", success_count
        )

        print(f"\n行业板块数据已保存到: {output_file}")
        print(f"成功获取了 {success_count}/{len(industry_boards)} 个行业板块的数据")

        # 统计信息
        total_stocks = sum(len(stocks) for stocks in all_industry_data.values())
        print(f"总计 {total_stocks} 条股票记录")

        return str(output_file)

    def fetch_all_board_data(
        self, delay: float = 0.1, skip_existing: bool = False
    ) -> dict[str, str]:
        """
        获取所有板块（概念+行业）的成份股数据

        Args:
            delay: 请求间隔时间（秒）
            skip_existing: 是否跳过已经抓取的板块

        Returns:
            包含概念和行业数据文件路径的字典
        """
        result = {}

        # 获取概念板块数据
        concept_file = self.fetch_all_concept_data(delay, skip_existing)
        if concept_file:
            result["concept"] = concept_file

        # 获取行业板块数据
        industry_file = self.fetch_all_industry_data(delay, skip_existing)
        if industry_file:
            result["industry"] = industry_file

        return result

    def _extract_stock_info(self, df: pd.DataFrame) -> list[dict]:
        """
        从DataFrame中提取股票信息

        Args:
            df: 包含股票数据的DataFrame

        Returns:
            股票信息列表
        """
        stocks_info = []
        for _, row in df.iterrows():
            stock_code = str(row["代码"])
            stock_name = str(row["名称"])
            full_code = self._convert_stock_code(stock_code)

            stocks_info.append(
                {"code": stock_code, "full_code": full_code, "name": stock_name}
            )

        return stocks_info
