{"name": "repo_rate_hist", "category": "interest_rate", "title": "", "description": "回购定盘利率数据", "target_address": "https://www.chinamoney.com.cn/chinese/bkfrr/", "parameters": [{"name": "start_date", "type": "str", "description": "start_date=\"20200930\"; 开始时间与结束时间需要在一年内", "required": false, "default": ""}, {"name": "end_date", "type": "str", "description": "end_date=\"20201029\"; 开始时间与结束时间需要在一年内", "required": false, "default": ""}], "output_fields": [{"name": "date", "type": "object", "description": ""}, {"name": "FR001", "type": "float64", "description": "注意单位: %"}, {"name": "FR007", "type": "float64", "description": "注意单位: %"}, {"name": "FR014", "type": "float64", "description": "注意单位: %"}, {"name": "FDR001", "type": "float64", "description": "注意单位: %"}, {"name": "FDR007", "type": "float64", "description": "注意单位: %"}, {"name": "FDR014", "type": "float64", "description": "注意单位: %"}], "example_code": "import akshare as ak\nrepo_rate_hist_df = ak.repo_rate_hist(start_date=\"20231001\", end_date=\"20240101\")\nprint(repo_rate_hist_df)", "source_url": "https://akshare.akfamily.xyz/data/interest_rate/interest_rate.html", "symbol_format": null, "time_type": "time_series", "time_interval": "day", "start_date": "20231007", "start_year": "2023", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}