{"name": "repo_rate_query", "category": "interest_rate", "title": "", "description": "回购定盘利率数据", "target_address": "https://www.chinamoney.com.cn/chinese/bkfrr/", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"回购定盘利率\"; choice of {\"回购定盘利率\", \"银银间回购定盘利率\"}", "required": false, "default": ""}], "output_fields": [{"name": "date", "type": "object", "description": ""}, {"name": "FR001", "type": "float64", "description": "注意单位: %"}, {"name": "FR007", "type": "float64", "description": "注意单位: %"}, {"name": "FR014", "type": "float64", "description": "注意单位: %"}], "example_code": "import akshare as ak\nrepo_rate_query_df = ak.repo_rate_query(symbol=\"回购定盘利率\")\nprint(repo_rate_query_df)", "source_url": "https://akshare.akfamily.xyz/data/interest_rate/interest_rate.html", "symbol_format": null, "time_type": "time_series", "time_interval": "day", "start_date": "20220905", "start_year": "2022", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}