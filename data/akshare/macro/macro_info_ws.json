{"name": "macro_info_ws", "category": "macro", "title": "", "description": "华尔街见闻-日历-宏观", "target_address": "https://wallstreetcn.com/calendar", "parameters": [{"name": "date", "type": "str", "description": "date=\"20240514\"", "required": false, "default": ""}], "output_fields": [{"name": "时间", "type": "object", "description": ""}, {"name": "地区", "type": "object", "description": ""}, {"name": "事件", "type": "object", "description": ""}, {"name": "重要性", "type": "int64", "description": ""}, {"name": "今值", "type": "float64", "description": ""}, {"name": "预期", "type": "float64", "description": ""}, {"name": "前值", "type": "float64", "description": ""}, {"name": "链接", "type": "object", "description": ""}], "example_code": "import akshare as ak\nmacro_info_ws_df = ak.macro_info_ws(date=\"20240514\")\nprint(macro_info_ws_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "time_series", "time_interval": null, "start_date": "20240514", "start_year": "2024", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}