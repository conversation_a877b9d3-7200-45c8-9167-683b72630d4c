{"name": "macro_china_hk_market_info", "category": "macro", "title": "", "description": "香港同业拆借报告, 数据区间从 20170320-至今", "target_address": "https://datacenter.jin10.com/reportType/dc_hk_market_info", "parameters": [], "output_fields": [{"name": "日期", "type": "object", "description": "日期"}, {"name": "O/N-定价", "type": "float64", "description": ""}, {"name": "O/N-涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "1W-定价", "type": "float64", "description": ""}, {"name": "1W-涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "2W-定价", "type": "float64", "description": ""}, {"name": "2W-涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "1M-定价", "type": "float64", "description": ""}, {"name": "1M-涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "3M-定价", "type": "float64", "description": ""}, {"name": "3M-涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "6M-定价", "type": "float64", "description": ""}, {"name": "6M-涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "9M-定价", "type": "float64", "description": ""}, {"name": "9M-涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "1Y-定价", "type": "float64", "description": ""}, {"name": "1Y-涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "ON-定价", "type": "float64", "description": ""}, {"name": "ON-涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "2M-定价", "type": "float64", "description": ""}, {"name": "2M-涨跌幅", "type": "float64", "description": "单位: 点"}], "example_code": "import akshare as ak\nmacro_china_hk_market_info_df = ak.macro_china_hk_market_info()\nprint(macro_china_hk_market_info_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "time_series", "time_interval": "day", "start_date": "20170320", "start_year": "2017", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}