{"name": "macro_china_nbs_nation", "category": "macro", "title": "", "description": "国家统计局全国数据通用接口，包括月度数据、季度数据、年度数据，具体指标见数据官网。", "target_address": "https://data.stats.gov.cn/easyquery.htm", "parameters": [{"name": "kind", "type": "str", "description": "数据类别，包括：月度数据、季度数据、年度数据。", "required": false, "default": ""}, {"name": "path", "type": "str", "description": "数据路径， 需与kind参数匹配，具体见官网，多层级之间使用  > 连接 。 示例：    国民经济核算 > 支出法国内生产总值   人口 > 总人口   金融业 > 保险系统机构、人员数 > 保险系统机构数", "required": false, "default": ""}, {"name": "period", "type": "str", "description": "时间区间 参考格式如下(英文逗号分割，且不能有多余空格)：    月：201201,201205    季：2012A,2012B,2012C,2012D    年：2012,2013     至今：2013-    最近：last10", "required": false, "default": ""}], "output_fields": [{"name": "2020年", "type": "float", "description": "2020年"}, {"name": "2021年", "type": "float", "description": "2021年"}, {"name": "2022年", "type": "float", "description": "2022年"}, {"name": "2023年", "type": "float", "description": "2023年"}, {"name": "2024年", "type": "float", "description": "2024年"}], "example_code": "import akshare as ak\nmacro_china_nbs_nation_df = ak.macro_china_nbs_nation(kind=\"年度数据\", path=\"人口 > 总人口\", period=\"LAST5\")\nprint(macro_china_nbs_nation_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}