{"name": "macro_china_swap_rate", "category": "macro", "title": "", "description": "国家统计局-FR007利率互换曲线历史数据", "target_address": "https://www.chinamoney.com.cn/chinese/bkcurvfxhis/?cfgItemType=72&curveType=FR007", "parameters": [{"name": "start_date", "type": "str", "description": "start_date=\"20231128\"；注意时间间隔", "required": false, "default": ""}, {"name": "end_date", "type": "str", "description": "end_date=\"20231130\"", "required": false, "default": ""}], "output_fields": [{"name": "日期", "type": "object", "description": ""}, {"name": "曲线名称", "type": "object", "description": ""}, {"name": "时刻", "type": "object", "description": ""}, {"name": "价格类型", "type": "object", "description": ""}, {"name": "1M", "type": "float64", "description": ""}, {"name": "3M", "type": "float64", "description": ""}, {"name": "6M", "type": "float64", "description": ""}, {"name": "9M", "type": "float64", "description": ""}, {"name": "1Y", "type": "float64", "description": ""}, {"name": "2Y", "type": "float64", "description": ""}, {"name": "3Y", "type": "float64", "description": ""}, {"name": "4Y", "type": "float64", "description": ""}, {"name": "5Y", "type": "float64", "description": ""}, {"name": "7Y", "type": "float64", "description": ""}, {"name": "10Y", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nmacro_china_swap_rate_df = ak.macro_china_swap_rate(start_date=\"20240501\", end_date=\"20240531\")\nprint(macro_china_swap_rate_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": null, "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": true, "tags": [], "analysts": []}