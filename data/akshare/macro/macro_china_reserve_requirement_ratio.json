{"name": "macro_china_reserve_requirement_ratio", "category": "macro", "title": "", "description": "国家统计局-存款准备金率", "target_address": "https://data.eastmoney.com/cjsj/ckzbj.html", "parameters": [], "output_fields": [{"name": "公布时间", "type": "object", "description": "XXXX年X月"}, {"name": "生效时间", "type": "object", "description": "XXXX年X月"}, {"name": "大型金融机构-调整前", "type": "float64", "description": "注意单位: %"}, {"name": "大型金融机构-调整后", "type": "float64", "description": "注意单位: %"}, {"name": "大型金融机构-调整幅度", "type": "float64", "description": "注意单位: %"}, {"name": "中小金融机构-调整前", "type": "float64", "description": "注意单位: %"}, {"name": "中小金融机构-调整后", "type": "float64", "description": "注意单位: %"}, {"name": "中小金融机构-调整幅度", "type": "float64", "description": "注意单位: %"}, {"name": "消息公布次日指数涨跌-上证", "type": "float64", "description": "注意单位: %"}, {"name": "消息公布次日指数涨跌-深证", "type": "float64", "description": "注意单位: %"}, {"name": "备注", "type": "object", "description": ""}], "example_code": "import akshare as ak\nmacro_china_reserve_requirement_ratio_df = ak.macro_china_reserve_requirement_ratio()\nprint(macro_china_reserve_requirement_ratio_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}