{"name": "macro_china_cpi", "category": "macro", "title": "", "description": "中国居民消费价格指数, 数据区间从 200801 至今, 月度数据", "target_address": "http://data.eastmoney.com/cjsj/cpi.html", "parameters": [], "output_fields": [{"name": "月份", "type": "object", "description": ""}, {"name": "全国-当月", "type": "float64", "description": ""}, {"name": "全国-同比增长", "type": "float64", "description": "注意单位: %"}, {"name": "全国-环比增长", "type": "float64", "description": "注意单位: %"}, {"name": "全国-累计", "type": "float64", "description": ""}, {"name": "城市-当月", "type": "float64", "description": ""}, {"name": "城市-同比增长", "type": "float64", "description": "注意单位: %"}, {"name": "城市-环比增长", "type": "float64", "description": "注意单位: %"}, {"name": "城市-累计", "type": "float64", "description": ""}, {"name": "农村-当月", "type": "float64", "description": ""}, {"name": "农村-同比增长", "type": "float64", "description": "注意单位: %"}, {"name": "农村-环比增长", "type": "float64", "description": "注意单位: %"}, {"name": "农村-累计", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nmacro_china_cpi_df = ak.macro_china_cpi()\nprint(macro_china_cpi_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}