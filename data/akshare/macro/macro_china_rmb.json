{"name": "macro_china_rmb", "category": "macro", "title": "", "description": "中国人民币汇率中间价报告, 数据区间从 20170103-20210513", "target_address": "https://datacenter.jin10.com/reportType/dc_rmb_data", "parameters": [], "output_fields": [{"name": "日期", "type": "object", "description": "日期"}, {"name": "美元/人民币_中间价", "type": "float64", "description": ""}, {"name": "美元/人民币_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "欧元/人民币_中间价", "type": "float64", "description": ""}, {"name": "欧元/人民币_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "100日元/人民币_中间价", "type": "float64", "description": ""}, {"name": "100日元/人民币_涨跌幅", "type": "float64", "description": "单位: 点对"}, {"name": "港元/人民币_中间价", "type": "float64", "description": ""}, {"name": "港元/人民币_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "英镑/人民币_中间价", "type": "float64", "description": ""}, {"name": "英镑/人民币_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "澳元/人民币_中间价", "type": "float64", "description": ""}, {"name": "澳元/人民币_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "新西兰元/人民币_中间价", "type": "float64", "description": ""}, {"name": "新西兰元/人民币_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "新加坡元/人民币_中间价", "type": "float64", "description": ""}, {"name": "新加坡元/人民币_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "瑞郎/人民币_中间价", "type": "float64", "description": ""}, {"name": "瑞郎/人民币_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "加元/人民币_中间价", "type": "float64", "description": ""}, {"name": "加元/人民币_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/马来西亚林吉特_中间价", "type": "float64", "description": ""}, {"name": "人民币/马来西亚林吉特_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/俄罗斯卢布_中间价", "type": "float64", "description": ""}, {"name": "人民币/俄罗斯卢布_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/南非兰特_中间价", "type": "float64", "description": ""}, {"name": "人民币/南非兰特_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/韩元_中间价", "type": "float64", "description": ""}, {"name": "人民币/韩元_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/阿联酋迪拉姆_中间价", "type": "float64", "description": ""}, {"name": "人民币/阿联酋迪拉姆_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/沙特里亚尔_中间价", "type": "float64", "description": ""}, {"name": "人民币/沙特里亚尔_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/匈牙利福林_中间价", "type": "float64", "description": ""}, {"name": "人民币/匈牙利福林_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/波兰兹罗提_中间价", "type": "float64", "description": ""}, {"name": "人民币/波兰兹罗提_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/丹麦克朗_中间价", "type": "float64", "description": ""}, {"name": "人民币/丹麦克朗_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/瑞典克朗_中间价", "type": "float64", "description": ""}, {"name": "人民币/瑞典克朗_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/丹麦克朗_中间价", "type": "float64", "description": ""}, {"name": "人民币/丹麦克朗_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/挪威克朗_中间价", "type": "float64", "description": ""}, {"name": "人民币/挪威克朗_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/土耳其里拉_中间价", "type": "float64", "description": ""}, {"name": "人民币/土耳其里拉_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/墨西哥比索_中间价", "type": "float64", "description": ""}, {"name": "人民币/墨西哥比索_涨跌幅", "type": "float64", "description": "单位: 点"}, {"name": "人民币/泰铢_中间价", "type": "float64", "description": ""}, {"name": "人民币/泰铢_涨跌幅", "type": "float64", "description": "单位: 点"}], "example_code": "import akshare as ak\nmacro_china_rmb_df = ak.macro_china_rmb()\nprint(macro_china_rmb_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "time_series", "time_interval": "day", "start_date": "20180206", "start_year": "2018", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}