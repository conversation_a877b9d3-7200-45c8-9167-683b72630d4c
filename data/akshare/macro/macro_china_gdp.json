{"name": "macro_china_gdp", "category": "macro", "title": "", "description": "中国国内生产总值, 数据区间从 200601 至今, 月度数据", "target_address": "http://data.eastmoney.com/cjsj/gdp.html", "parameters": [], "output_fields": [{"name": "季度", "type": "object", "description": ""}, {"name": "国内生产总值-绝对值", "type": "float64", "description": "注意单位: 亿元"}, {"name": "国内生产总值-同比增长", "type": "float64", "description": "注意单位: %"}, {"name": "第一产业-绝对值", "type": "float64", "description": "注意单位: 亿元"}, {"name": "第一产业-同比增长", "type": "float64", "description": "注意单位: %"}, {"name": "第二产业-绝对值", "type": "float64", "description": "注意单位: 亿元"}, {"name": "第二产业-同比增长", "type": "float64", "description": "注意单位: %"}, {"name": "第三产业-绝对值", "type": "float64", "description": "注意单位: 亿元"}, {"name": "第三产业-同比增长", "type": "float64", "description": "注意单位: %"}], "example_code": "import akshare as ak\nmacro_china_gdp_df = ak.macro_china_gdp()\nprint(macro_china_gdp_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}