{"name": "macro_euro_current_account_mom", "category": "macro", "title": "", "description": "欧元区经常帐报告, 数据区间从 ********-至今", "target_address": "https://datacenter.jin10.com/reportType/dc_eurozone_current_account_mom", "parameters": [], "output_fields": [{"name": "商品", "type": "object", "description": ""}, {"name": "日期", "type": "object", "description": ""}, {"name": "今值", "type": "float64", "description": "注意单位: %"}, {"name": "预测值", "type": "float64", "description": "注意单位: %"}, {"name": "前值", "type": "float64", "description": "注意单位: %"}], "example_code": "import akshare as ak\nmacro_euro_current_account_mom_df = ak.macro_euro_current_account_mom()\nprint(macro_euro_current_account_mom_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "time_series", "time_interval": "year", "start_date": "********", "start_year": "1999", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}