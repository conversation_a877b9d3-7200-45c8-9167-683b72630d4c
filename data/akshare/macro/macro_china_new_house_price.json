{"name": "macro_china_new_house_price", "category": "macro", "title": "", "description": "中国新房价指数月度数据, 数据区间从 201101-至今", "target_address": "http://data.eastmoney.com/cjsj/newhouse.html", "parameters": [{"name": "city_first", "type": "str", "description": "city_first=\"北京\"; 城市列表见目标网站", "required": false, "default": ""}, {"name": "city_second", "type": "str", "description": "city_second=\"上海\"; 城市列表见目标网站", "required": false, "default": ""}], "output_fields": [{"name": "日期", "type": "object", "description": "日期"}, {"name": "城市", "type": "object", "description": ""}, {"name": "新建商品住宅价格指数-环比", "type": "float64", "description": ""}, {"name": "新建商品住宅价格指数-同比", "type": "float64", "description": ""}, {"name": "新建商品住宅价格指数-定基", "type": "float64", "description": ""}, {"name": "二手住宅价格指数-环比", "type": "float64", "description": ""}, {"name": "二手住宅价格指数-同比", "type": "float64", "description": ""}, {"name": "二手住宅价格指数-定基", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nmacro_china_new_house_price_df = ak.macro_china_new_house_price(city_first=\"北京\", city_second=\"上海\")\nprint(macro_china_new_house_price_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "time_series", "time_interval": null, "start_date": "20110101", "start_year": "2011", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}