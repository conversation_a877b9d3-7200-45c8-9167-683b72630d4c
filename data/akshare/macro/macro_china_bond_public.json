{"name": "macro_china_bond_public", "category": "macro", "title": "", "description": "中国外汇交易中心暨全国银行间同业拆借中心-债券信息披露-新债发行; 近期债券发行数据", "target_address": "https://www.chinamoney.com.cn/chinese/xzjfx/", "parameters": [], "output_fields": [{"name": "债券全称", "type": "object", "description": ""}, {"name": "债券类型", "type": "object", "description": ""}, {"name": "发行日期", "type": "object", "description": ""}, {"name": "计息方式", "type": "object", "description": ""}, {"name": "价格", "type": "float64", "description": "注意单位: 元"}, {"name": "债券期限", "type": "object", "description": ""}, {"name": "计划发行量", "type": "float64", "description": "注意单位: 亿元"}, {"name": "债券评级", "type": "object", "description": ""}], "example_code": "import akshare as ak\nmacro_china_bond_public_df = ak.macro_china_bond_public()\nprint(macro_china_bond_public_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": null, "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": true, "tags": [], "analysts": []}