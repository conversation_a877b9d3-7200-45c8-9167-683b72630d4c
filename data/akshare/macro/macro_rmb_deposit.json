{"name": "macro_rmb_deposit", "category": "macro", "title": "", "description": "同花顺-数据中心-宏观数据-人民币存款余额", "target_address": "https://data.10jqka.com.cn/macro/rmb/", "parameters": [], "output_fields": [{"name": "月份", "type": "object", "description": ""}, {"name": "新增存款-数量", "type": "float64", "description": ""}, {"name": "新增存款-同比", "type": "object", "description": ""}, {"name": "新增存款-环比", "type": "object", "description": ""}, {"name": "新增企业存款-数量", "type": "float64", "description": ""}, {"name": "新增企业存款-同比", "type": "object", "description": ""}, {"name": "新增企业存款-环比", "type": "object", "description": ""}, {"name": "新增储蓄存款-数量", "type": "float64", "description": ""}, {"name": "新增储蓄存款-同比", "type": "object", "description": ""}, {"name": "新增储蓄存款-环比", "type": "object", "description": ""}, {"name": "新增其他存款-数量", "type": "float64", "description": ""}, {"name": "新增其他存款-同比", "type": "object", "description": ""}, {"name": "新增其他存款-环比", "type": "object", "description": ""}], "example_code": "import akshare as ak\nmacro_rmb_deposit_df = ak.macro_rmb_deposit()\nprint(macro_rmb_deposit_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}