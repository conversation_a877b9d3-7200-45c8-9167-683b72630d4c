{"name": "news_economic_baidu", "category": "macro", "title": "", "description": "全球宏观指标重大事件", "target_address": "https://gushitong.baidu.com/calendar", "parameters": [{"name": "date", "type": "str", "description": "date=\"20241107\"", "required": false, "default": ""}], "output_fields": [{"name": "日期", "type": "object", "description": ""}, {"name": "时间", "type": "object", "description": ""}, {"name": "地区", "type": "object", "description": ""}, {"name": "事件", "type": "object", "description": ""}, {"name": "公布", "type": "float64", "description": ""}, {"name": "预期", "type": "float64", "description": ""}, {"name": "前值", "type": "float64", "description": ""}, {"name": "重要性", "type": "float64", "description": "数值越大越重要"}], "example_code": "import akshare as ak\nnews_economic_baidu_df = ak.news_economic_baidu(date=\"20241107\")\nprint(news_economic_baidu_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": "min", "start_date": "20241107", "start_year": "2024", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}