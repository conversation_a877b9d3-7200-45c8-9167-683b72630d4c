{"name": "macro_china_xfzxx", "category": "macro", "title": "", "description": "东方财富网-消费者信心指数", "target_address": "https://data.eastmoney.com/cjsj/xfzxx.html", "parameters": [], "output_fields": [{"name": "月份", "type": "object", "description": ""}, {"name": "消费者信心指数-指数值", "type": "float64", "description": ""}, {"name": "消费者信心指数-同比增长", "type": "float64", "description": "注意单位: %"}, {"name": "消费者信心指数-环比增长", "type": "float64", "description": "注意单位: %"}, {"name": "消费者满意指数-指数值", "type": "float64", "description": ""}, {"name": "消费者满意指数-同比增长", "type": "float64", "description": "注意单位: %"}, {"name": "消费者满意指数-环比增长", "type": "float64", "description": "注意单位: %"}, {"name": "消费者预期指数-指数值", "type": "float64", "description": ""}, {"name": "消费者预期指数-同比增长", "type": "float64", "description": "注意单位: %"}, {"name": "消费者预期指数-环比增长", "type": "float64", "description": "注意单位: %"}], "example_code": "import akshare as ak\nmacro_china_xfzxx_df = ak.macro_china_xfzxx()\nprint(macro_china_xfzxx_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}