{"name": "macro_china_nbs_region", "category": "macro", "title": "", "description": "国家统计局地区数据通用接口，包括分省月度数据、分省季度数据、分省年度数据、主要城市月度价格、主要城市年度数据、港澳台月度数据、港澳台年度数据，具体指标见数据官网。", "target_address": "https://data.stats.gov.cn/easyquery.htm", "parameters": [{"name": "kind", "type": "str", "description": "数据类别，包括：分省月度数据、分省季度数据、分省年度数据、主要城市月度价格、主要城市年度数据、港澳台月度数据、港澳台年度数据。", "required": false, "default": ""}, {"name": "path", "type": "str", "description": "数据路径， 需与kind匹配，具体见官网，多层级之间使用  > 连接 。 示例：   国民经济核算 > 地区生产总值   财政 > 地方财政收入", "required": false, "default": ""}, {"name": "indicator", "type": "Union[str, None]", "description": "指定指标，表示在当前path下可选择的指标。在指定region参数的情况下，此参数可以设置为None，此时将获取指定地区下所有可选指标的值。indicator和region参数不能同时为None。", "required": false, "default": ""}, {"name": "region", "type": "Union[str, None]", "description": "指定地区，为可选指标。指定时表示仅获取当前地区下的数据。", "required": false, "default": ""}, {"name": "period", "type": "str", "description": "时间区间参考格式如下(英文逗号分割，且不能有多余空格)：    月：201201,201205    季：2012A,2012B,2012C,2012D    年：2012,2013     至今：2013-    最近：last10", "required": false, "default": ""}], "output_fields": [{"name": "2022年第一季度", "type": "float", "description": "2022年第一季度"}, {"name": "2022年第三季度", "type": "float", "description": "2022年第三季度"}, {"name": "2022年第二季度", "type": "float", "description": "2022年第二季度"}, {"name": "2022年第四季度", "type": "float", "description": "2022年第四季度"}], "example_code": "import akshare as ak\nmacro_china_nbs_region_df = ak.macro_china_nbs_region(kind=\"分省季度数据\", path=\"国民经济核算 > 地区生产总值\", period=\"last3\", indicator=None, region=\"河北省\")\nprint(macro_china_nbs_region_df)\nmacro_china_nbs_region_df = ak.macro_china_nbs_region(kind=\"分省季度数据\", path=\"人民生活 > 居民人均可支配收入\", indicator='居民人均可支配收入_累计值(元)', period=\"2022\")\nprint(macro_china_nbs_region_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}