{"name": "macro_china_stock_market_cap", "category": "macro", "title": "", "description": "全国股票交易统计表, 数据区间从 200801 至今, 月度数据", "target_address": "http://data.eastmoney.com/cjsj/gpjytj.html", "parameters": [], "output_fields": [{"name": "数据日期", "type": "object", "description": "年度和月份"}, {"name": "发行总股本-上海", "type": "float64", "description": "注意单位: 亿元"}, {"name": "发行总股本-深圳", "type": "float64", "description": "注意单位: 亿元"}, {"name": "市价总值-上海", "type": "float64", "description": "注意单位: 亿元"}, {"name": "市价总值-深圳", "type": "float64", "description": "注意单位: 亿元"}, {"name": "成交金额-上海", "type": "float64", "description": "注意单位: 亿元"}, {"name": "成交金额-深圳", "type": "float64", "description": "注意单位: 亿元"}, {"name": "成交量-上海", "type": "float64", "description": ""}, {"name": "成交量-深圳", "type": "float64", "description": ""}, {"name": "A股最高综合股价指数-上海", "type": "float64", "description": ""}, {"name": "A股最高综合股价指数-深圳", "type": "float64", "description": ""}, {"name": "A股最低综合股价指数-上海", "type": "float64", "description": ""}, {"name": "A股最低综合股价指数-深圳", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nmacro_china_stock_market_cap_df = ak.macro_china_stock_market_cap()\nprint(macro_china_stock_market_cap_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}