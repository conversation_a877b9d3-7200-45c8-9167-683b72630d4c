{"name": "macro_china_hgjck", "category": "macro", "title": "", "description": "中国海关进出口增减情况一览表, 数据区间从 200801 至今, 月度数据", "target_address": "https://data.eastmoney.com/cjsj/hgjck.html", "parameters": [], "output_fields": [{"name": "月份", "type": "object", "description": ""}, {"name": "当月出口额-金额", "type": "float64", "description": "注意单位: 亿美元"}, {"name": "当月出口额-同比增长", "type": "float64", "description": "注意单位: %"}, {"name": "当月出口额-环比增长", "type": "float64", "description": "注意单位: %"}, {"name": "当月进口额-金额", "type": "float64", "description": "注意单位: 亿美元"}, {"name": "当月进口额-同比增长", "type": "float64", "description": "注意单位: %"}, {"name": "当月进口额-环比增长", "type": "float64", "description": "注意单位: %"}, {"name": "累计出口额-金额", "type": "float64", "description": "注意单位: 亿美元"}, {"name": "累计出口额-同比增长", "type": "float64", "description": "注意单位: %"}, {"name": "累计进口额-金额", "type": "float64", "description": "注意单位: 亿美元"}, {"name": "累计进口额-同比增长", "type": "float64", "description": "注意单位: %"}], "example_code": "import akshare as ak\nmacro_china_hgjck_df = ak.macro_china_hgjck()\nprint(macro_china_hgjck_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}