{"name": "macro_china_au_report", "category": "macro", "title": "", "description": "上海黄金交易所报告, 数据区间从 20140905-至今", "target_address": "https://datacenter.jin10.com/reportType/dc_sge_report", "parameters": [], "output_fields": [{"name": "日期", "type": "object", "description": ""}, {"name": "商品", "type": "object", "description": ""}, {"name": "开盘价", "type": "float64", "description": ""}, {"name": "最高价", "type": "float64", "description": ""}, {"name": "最低价", "type": "float64", "description": ""}, {"name": "收盘价", "type": "float64", "description": ""}, {"name": "涨跌", "type": "float64", "description": ""}, {"name": "涨跌幅", "type": "float64", "description": ""}, {"name": "加权平均价", "type": "float64", "description": ""}, {"name": "成交量", "type": "float64", "description": ""}, {"name": "成交金额", "type": "float64", "description": ""}, {"name": "持仓量", "type": "float64", "description": ""}, {"name": "交收方向", "type": "object", "description": ""}, {"name": "交收量", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nmacro_china_au_report_df = ak.macro_china_au_report()\nprint(macro_china_au_report_df)", "source_url": "https://akshare.akfamily.xyz/data/macro/macro.html", "symbol_format": null, "time_type": "time_series", "time_interval": null, "start_date": "20140905", "start_year": "2014", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}