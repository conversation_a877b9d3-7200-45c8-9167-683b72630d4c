{"name": "cost_living", "category": "others", "title": "", "description": "世界各大城市生活成本数据", "target_address": "https://expatistan.com/cost-of-living/index", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"world\", 默认, 返回所有城市数据, 其他城市请查看 城市一览表", "required": false, "default": ""}, {"name": "城市一览表", "type": "名称", "description": "类型", "required": false, "default": ""}, {"name": "europe", "type": "欧洲", "description": "north-america", "required": false, "default": ""}, {"name": "北美洲", "type": "latin-america", "description": "拉丁美洲", "required": false, "default": ""}, {"name": "asia", "type": "亚洲", "description": "middle-east", "required": false, "default": ""}, {"name": "中东", "type": "africa", "description": "非洲", "required": false, "default": ""}, {"name": "oceania", "type": "大洋洲", "description": "world", "required": false, "default": ""}], "output_fields": [{"name": "rank", "type": "object", "description": "排名"}, {"name": "city", "type": "object", "description": "城市名称"}, {"name": "index", "type": "int64", "description": "价格指数"}], "example_code": "import akshare as ak\ncost_living_df = ak.cost_living(symbol=\"world\")\nprint(cost_living_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": null, "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": true, "tags": [], "analysts": []}