{"name": "car_market_country_cpca", "category": "others", "title": "", "description": "乘联会-统计数据-国别细分市场", "target_address": "http://data.cpcadata.com/CountryMarket", "parameters": [], "output_fields": [{"name": "月份", "type": "object", "description": ""}, {"name": "自主", "type": "float64", "description": "注意单位: 万辆"}, {"name": "德系", "type": "float64", "description": "注意单位: 万辆"}, {"name": "日系", "type": "float64", "description": "注意单位: 万辆"}, {"name": "法系", "type": "float64", "description": "注意单位: 万辆"}, {"name": "美系", "type": "float64", "description": "注意单位: 万辆"}, {"name": "韩系", "type": "float64", "description": "注意单位: 万辆"}, {"name": "其他欧系", "type": "float64", "description": "注意单位: 万辆"}], "example_code": "import akshare as ak\ncar_market_country_cpca_df = ak.car_market_country_cpca()\nprint(car_market_country_cpca_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}