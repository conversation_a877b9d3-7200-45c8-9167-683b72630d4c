{"name": "stock_js_weibo_report", "category": "others", "title": "", "description": "微博舆情报告中近期受关注的股票", "target_address": "https://datacenter.jin10.com/market", "parameters": [{"name": "time_period", "type": "str", "description": "time_period=\"CNHOUR12\"; 详见下表time_period参数一览表, 可通过调用 stock_js_weibo_nlp_time 获取", "required": false, "default": ""}, {"name": "time_period 参数一览表", "type": "参数", "description": "说明", "required": false, "default": ""}, {"name": "CNHOUR2", "type": "2小时", "description": "CNHOUR6", "required": false, "default": ""}, {"name": "6小时", "type": "CNHOUR12", "description": "12小时", "required": false, "default": ""}, {"name": "CNHOUR24", "type": "1天", "description": "CNDAY7", "required": false, "default": ""}, {"name": "1周", "type": "CNDAY30", "description": "1月", "required": false, "default": ""}], "output_fields": [{"name": "name", "type": "str", "description": "股票名称"}, {"name": "rate", "type": "str", "description": "人气排行指数"}], "example_code": "import akshare as ak\nstock_js_weibo_report_df = ak.stock_js_weibo_report(time_period=\"CNHOUR12\")\nprint(stock_js_weibo_report_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}