{"name": "air_quality_rank", "category": "others", "title": "", "description": "获取指定 date 时间点上所有城市(168个)的空气质量数据", "target_address": "https://www.zq12369.com/environment.php", "parameters": [{"name": "date", "type": "str", "description": "date=\"\"; \"\": 当前时刻空气质量排名, 默认; \"20200312\": 当日空气质量排名; \"202003\": 当月空气质量排名; \"2019\": 当年空气质量排名;", "required": false, "default": ""}], "output_fields": [{"name": "降序", "type": "str", "description": "排名"}, {"name": "省份", "type": "str", "description": ""}, {"name": "城市", "type": "str", "description": "AQI float"}, {"name": "空气质量", "type": "str", "description": ""}, {"name": "PM2.5浓度", "type": "str", "description": ""}, {"name": "首要污染物", "type": "str", "description": ""}], "example_code": "import akshare as ak\nair_quality_rank_df = ak.air_quality_rank(date=\"\")\nprint(air_quality_rank_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}