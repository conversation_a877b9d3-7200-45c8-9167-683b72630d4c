{"name": "movie_boxoffice_yearly_first_week", "category": "others", "title": "", "description": "指定日期所在年度的年度首周票房数据", "target_address": "https://www.endata.com.cn/BoxOffice/BO/Year/firstWeek.html", "parameters": [{"name": "date", "type": "str", "description": "date=\"20201018\"; 输入具体的日期即可", "required": false, "default": ""}], "output_fields": [{"name": "排序", "type": "int64", "description": "票房排名"}, {"name": "影片名称", "type": "object", "description": ""}, {"name": "类型", "type": "object", "description": ""}, {"name": "首周票房", "type": "int64", "description": "注意单位: 万"}, {"name": "占总票房比重", "type": "int64", "description": "注意单位: %"}, {"name": "场均人次", "type": "int64", "description": ""}, {"name": "国家及地区", "type": "object", "description": ""}, {"name": "上映日期", "type": "object", "description": ""}, {"name": "首周天数", "type": "int64", "description": ""}], "example_code": "import akshare as ak\nmovie_boxoffice_yearly_first_week_df = ak.movie_boxoffice_yearly_first_week(date=\"20201018\")\nprint(movie_boxoffice_yearly_first_week_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "time_series", "time_interval": "year", "start_date": "20200110", "start_year": "2020", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}