{"name": "car_market_cate_cpca", "category": "others", "title": "", "description": "乘联会-统计数据-车型大类", "target_address": "http://data.cpcadata.com/CategoryMarket", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"轿车\"; choice of {\"轿车\", \"MPV\", \"SUV\", \"占比\"}", "required": false, "default": ""}, {"name": "indicator", "type": "str", "description": "indicator=\"批发\"; choice of {\"批发\", \"零售\"}", "required": false, "default": ""}], "output_fields": [{"name": "月份", "type": "object", "description": ""}, {"name": "{前一个年份}年", "type": "float64", "description": "注意单位: 万辆"}, {"name": "{当前年份}年", "type": "float64", "description": "注意单位: 万辆"}], "example_code": "import akshare as ak\ncar_market_cate_cpca_df = ak.car_market_cate_cpca(symbol=\"轿车\", indicator=\"批发\")\nprint(car_market_cate_cpca_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": null, "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": true, "tags": [], "analysts": []}