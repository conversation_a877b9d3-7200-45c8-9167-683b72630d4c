{"name": "movie_boxoffice_monthly", "category": "others", "title": "", "description": "获取指定日期所在月份的票房数据, 每月5号更新上月票房，并补充之前两个月票房", "target_address": "https://www.endata.com.cn/BoxOffice/BO/Month/oneMonth.html", "parameters": [{"name": "date", "type": "str", "description": "date=\"20201019\"; 输入具体的日期即可", "required": false, "default": ""}], "output_fields": [{"name": "排序", "type": "int64", "description": "票房排名"}, {"name": "影片名称", "type": "object", "description": ""}, {"name": "单月票房", "type": "int64", "description": "注意单位: 万"}, {"name": "月度占比", "type": "float64", "description": "注意单位: %"}, {"name": "平均票价", "type": "int64", "description": ""}, {"name": "场均人次", "type": "int64", "description": ""}, {"name": "上映日期", "type": "object", "description": ""}, {"name": "口碑指数", "type": "float64", "description": ""}, {"name": "月内天数", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nmovie_boxoffice_monthly_df = ak.movie_boxoffice_monthly(date=\"20240218\")\nprint(movie_boxoffice_monthly_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "time_series", "time_interval": "week", "start_date": "20231220", "start_year": "2023", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}