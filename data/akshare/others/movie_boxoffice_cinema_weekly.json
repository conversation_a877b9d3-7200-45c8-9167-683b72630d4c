{"name": "movie_boxoffice_cinema_weekly", "category": "others", "title": "", "description": "指定日期的完整周各影院的票房数据", "target_address": "https://www.endata.com.cn/BoxOffice/BO/Cinema/week.html", "parameters": [{"name": "date", "type": "str", "description": "date=\"20240219\"; 输入具体的日期即可", "required": false, "default": ""}], "output_fields": [{"name": "排序", "type": "int64", "description": "票房排名"}, {"name": "影院名称", "type": "object", "description": ""}, {"name": "当周票房", "type": "float64", "description": "注意单位: 万"}, {"name": "单银幕票房", "type": "float64", "description": "注意单位: 元"}, {"name": "场均人次", "type": "float64", "description": ""}, {"name": "单日单厅票房", "type": "float64", "description": ""}, {"name": "单日单厅场次", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nmovie_boxoffice_cinema_weekly_df = ak.movie_boxoffice_cinema_weekly(date=\"20240219\")\nprint(movie_boxoffice_cinema_weekly_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": "week", "start_date": "20240219", "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}