{"name": "index_bloomberg_billionaires", "category": "others", "title": "", "description": "彭博亿万富豪指数, 全球前 500 名; 该接口需要使用代理访问", "target_address": "https://www.bloomberg.com/billionaires/", "parameters": [], "output_fields": [{"name": "rank", "type": "str", "description": "Rank"}, {"name": "name", "type": "str", "description": "Name"}, {"name": "total_net_worth", "type": "str", "description": "Total net worth"}, {"name": "last_change", "type": "str", "description": "$ Last change"}, {"name": "YTD_change", "type": "str", "description": "$ YTD change"}, {"name": "country", "type": "str", "description": "Country"}, {"name": "industry", "type": "str", "description": "Industry"}], "example_code": "import akshare as ak\nindex_bloomberg_billionaires_df = ak.index_bloomberg_billionaires()\nprint(index_bloomberg_billionaires_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": null, "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": true, "tags": [], "analysts": []}