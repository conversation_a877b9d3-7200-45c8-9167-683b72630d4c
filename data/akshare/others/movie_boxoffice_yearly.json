{"name": "movie_boxoffice_yearly", "category": "others", "title": "", "description": "指定日期所在年度的票房数据", "target_address": "https://www.endata.com.cn/BoxOffice/BO/Year/index.html", "parameters": [{"name": "date", "type": "str", "description": "date=\"20240218\"; 输入具体的日期即可", "required": false, "default": ""}], "output_fields": [{"name": "排序", "type": "int64", "description": "票房排名"}, {"name": "影片名称", "type": "object", "description": ""}, {"name": "类型", "type": "object", "description": ""}, {"name": "总票房", "type": "int64", "description": "注意单位: 万"}, {"name": "平均票价", "type": "int64", "description": ""}, {"name": "场均人次", "type": "float64", "description": ""}, {"name": "国家及地区", "type": "object", "description": ""}, {"name": "上映日期", "type": "object", "description": ""}], "example_code": "import akshare as ak\nmovie_boxoffice_yearly_df = ak.movie_boxoffice_yearly(date=\"20240218\")\nprint(movie_boxoffice_yearly_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "time_series", "time_interval": "day", "start_date": "20231229", "start_year": "2023", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}