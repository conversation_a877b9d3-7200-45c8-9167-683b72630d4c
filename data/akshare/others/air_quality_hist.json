{"name": "air_quality_hist", "category": "others", "title": "", "description": "指定城市和数据频率下并且在指定时间段内的空气质量数据", "target_address": "https://www.zq12369.com/", "parameters": [{"name": "city", "type": "str", "description": "city=\"北京\"; 调用 ak.air_city_table() 接口获取所有城市列表", "required": false, "default": ""}, {"name": "period", "type": "str", "description": "period=\"day\"; \"hour\": 每小时一个数据, 由于数据量比较大, 下载较慢; \"day\": 每天一个数据; \"month\": 每个月一个数据", "required": false, "default": ""}, {"name": "start_date", "type": "str", "description": "start_date=\"20200320\"; 注意 start_date 和 end_date 跨度不宜过长", "required": false, "default": ""}, {"name": "end_date", "type": "str", "description": "end_date=\"20200427\"; 注意 start_date 和 end_date 跨度不宜过长", "required": false, "default": ""}], "output_fields": [{"name": "time", "type": "object", "description": "日期时间索引"}, {"name": "aqi", "type": "object", "description": "AQI"}, {"name": "pm2_5", "type": "float64", "description": "PM2.5"}, {"name": "pm10", "type": "object", "description": "PM10"}, {"name": "co", "type": "float64", "description": "CO"}, {"name": "no2", "type": "object", "description": "NO2"}, {"name": "o3", "type": "object", "description": "O3"}, {"name": "so2", "type": "object", "description": "SO2"}, {"name": "complexindex", "type": "object", "description": "综合指数"}, {"name": "rank", "type": "object", "description": "排名"}, {"name": "primary_pollutant", "type": "object", "description": "主要污染物"}, {"name": "temp", "type": "object", "description": "温度"}, {"name": "humi", "type": "object", "description": "湿度"}, {"name": "windlevel", "type": "object", "description": "风级"}, {"name": "winddirection", "type": "object", "description": "风向"}, {"name": "weather", "type": "object", "description": "天气"}], "example_code": "import akshare as ak\nair_quality_hist_df = ak.air_quality_hist(city=\"北京\", period=\"hour\", start_date=\"20200425\", end_date=\"20200427\")\nprint(air_quality_hist_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": "20200425", "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}