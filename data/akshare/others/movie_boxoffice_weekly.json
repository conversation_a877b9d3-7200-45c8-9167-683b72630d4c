{"name": "movie_boxoffice_weekly", "category": "others", "title": "", "description": "指定日期所在完整周的票房数据, 影片周票房数据初始更新周期为每周二，下周二补充数据", "target_address": "https://www.endata.com.cn/BoxOffice/BO/Week/oneWeek.html", "parameters": [{"name": "date", "type": "str", "description": "date=\"20240218\"; 指定日期所在周必须已经完整", "required": false, "default": ""}], "output_fields": [{"name": "排序", "type": "int64", "description": "票房排名"}, {"name": "影片名称", "type": "object", "description": ""}, {"name": "排名变化", "type": "int64", "description": ""}, {"name": "单周票房", "type": "int64", "description": "注意单位: 万"}, {"name": "环比变化", "type": "int64", "description": "注意单位: %"}, {"name": "累计票房", "type": "int64", "description": "注意单位: 万"}, {"name": "平均票价", "type": "int64", "description": ""}, {"name": "场均人次", "type": "int64", "description": ""}, {"name": "口碑指数", "type": "float64", "description": ""}, {"name": "上映天数", "type": "int64", "description": ""}], "example_code": "import akshare as ak\nmovie_boxoffice_weekly_df = ak.movie_boxoffice_weekly(date=\"20240218\")\nprint(movie_boxoffice_weekly_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": "week", "start_date": "20240218", "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}