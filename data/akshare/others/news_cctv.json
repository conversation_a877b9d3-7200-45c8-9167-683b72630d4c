{"name": "news_cctv", "category": "others", "title": "", "description": "新闻联播文字稿, 数据区间从 20160330-至今", "target_address": "https://tv.cctv.com/lm/xwlb", "parameters": [{"name": "date", "type": "str", "description": "date=\"20240424\";  20160330-至今", "required": false, "default": ""}], "output_fields": [{"name": "date", "type": "object", "description": "新闻日期"}, {"name": "title", "type": "object", "description": "新闻标题"}, {"name": "content", "type": "object", "description": "新闻内容"}], "example_code": "import akshare as ak\nnews_cctv_df = ak.news_cctv(date=\"20240424\")\nprint(news_cctv_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": "20240424", "start_year": "2024", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}