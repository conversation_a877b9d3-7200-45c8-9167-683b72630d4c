{"name": "car_market_segment_cpca", "category": "others", "title": "", "description": "乘联会-统计数据-级别细分市场", "target_address": "http://data.cpcadata.com/SegmentMarket", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"轿车\"; choice of {\"轿车\", \"MPV\", \"SUV\"}", "required": false, "default": ""}], "output_fields": [{"name": "月份", "type": "object", "description": ""}, {"name": "A00", "type": "float64", "description": "注意单位: 万辆"}, {"name": "A0", "type": "float64", "description": "注意单位: 万辆"}, {"name": "A", "type": "float64", "description": "注意单位: 万辆"}, {"name": "B", "type": "float64", "description": "注意单位: 万辆"}, {"name": "C", "type": "float64", "description": "注意单位: 万辆"}], "example_code": "import akshare as ak\ncar_market_segment_cpca_df = ak.car_market_segment_cpca(symbol=\"轿车\")\nprint(car_market_segment_cpca_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}