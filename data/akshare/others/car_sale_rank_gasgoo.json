{"name": "car_sale_rank_gasgoo", "category": "others", "title": "", "description": "盖世汽车资讯的汽车销量排行榜数据", "target_address": "https://i.gasgoo.com/data/ranking", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"车型榜\"; choice of {\"车企榜\", \"品牌榜\", \"车型榜\"}", "required": false, "default": ""}, {"name": "date", "type": "str", "description": "date=\"202104\"; 指定到月份即可", "required": false, "default": ""}], "output_fields": [{"name": "品牌", "type": "object", "description": ""}, {"name": "{当前年份}-{当前月份}", "type": "int64", "description": ""}, {"name": "{当前月份}月同比", "type": "object", "description": "注意单位: %"}, {"name": "{当前月份}月环比", "type": "object", "description": "注意单位: %"}, {"name": "{年份}-1到{当前年份}", "type": "int64", "description": ""}, {"name": "{前一年年份}-1到{当前年份}", "type": "float64", "description": ""}, {"name": "{前二年年份}-1到{当前年份}", "type": "float64", "description": ""}], "example_code": "import akshare as ak\ncar_sale_rank_gasgoo_df = ak.car_sale_rank_gasgoo(symbol=\"品牌榜\", date=\"202311\")\nprint(car_sale_rank_gasgoo_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}