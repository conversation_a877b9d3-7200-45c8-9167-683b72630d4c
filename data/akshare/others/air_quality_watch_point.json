{"name": "air_quality_watch_point", "category": "others", "title": "", "description": "获取每个城市的所有空气质量监测点的数据", "target_address": "https://www.zq12369.com/environment.php", "parameters": [{"name": "city", "type": "object", "description": "city=\"杭州\"; 调用 ak.air_city_table() 接口获取所有城市列表", "required": false, "default": ""}, {"name": "start_date", "type": "object", "description": "start_date=\"2018-01-01\"", "required": false, "default": ""}, {"name": "end_date", "type": "object", "description": "end_date=\"2020-04-27\"", "required": false, "default": ""}], "output_fields": [{"name": "pointname", "type": "object", "description": "监测点名称"}, {"name": "aqi", "type": "float64", "description": "AQI"}, {"name": "pm2_5", "type": "float64", "description": "PM2.5"}, {"name": "pm10", "type": "float64", "description": "PM10"}, {"name": "no2", "type": "float64", "description": "NO2"}, {"name": "so2", "type": "float64", "description": "SO2"}, {"name": "o3", "type": "float64", "description": "O3"}, {"name": "co", "type": "float64", "description": "CO"}], "example_code": "import akshare as ak\nair_quality_watch_point_df = ak.air_quality_watch_point(city=\"杭州\", start_date=\"2018-01-01\", end_date=\"2020-04-27\")\nprint(air_quality_watch_point_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": null, "time_interval": null, "start_date": "20180101", "start_year": null, "empty_result": true, "runtime_error": false, "tags": [], "analysts": []}