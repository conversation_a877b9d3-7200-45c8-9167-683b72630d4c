{"name": "movie_boxoffice_daily", "category": "others", "title": "", "description": "指定日期的电影票房数据, 每日 10:30, 12:30更新日票房，16:30 同时补充前 7 日票房", "target_address": "https://www.endata.com.cn/BoxOffice/BO/Day/index.html", "parameters": [{"name": "date", "type": "str", "description": "date=\"20240219\"; 只能选择最近的日期", "required": false, "default": ""}], "output_fields": [{"name": "排序", "type": "int64", "description": "票房排名"}, {"name": "影片名称", "type": "object", "description": ""}, {"name": "单日票房", "type": "int64", "description": "注意单位: 万"}, {"name": "环比变化", "type": "float64", "description": "注意单位: %"}, {"name": "累计票房", "type": "int64", "description": "注意单位: 万"}, {"name": "平均票价", "type": "int64", "description": "注意单位: 元"}, {"name": "场均人次", "type": "int64", "description": ""}, {"name": "口碑指数", "type": "float64", "description": ""}, {"name": "上映天数", "type": "int64", "description": ""}], "example_code": "import akshare as ak\nmovie_boxoffice_daily_df = ak.movie_boxoffice_daily(date=\"20240219\")\nprint(movie_boxoffice_daily_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": "day", "start_date": "20240219", "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}