{"name": "fortune_rank", "category": "others", "title": "", "description": "指定年份财富世界 500 强公司排行榜", "target_address": "https://www.fortunechina.com/fortune500/node_65.htm", "parameters": [{"name": "year", "type": "str", "description": "year=\"2023\"", "required": false, "default": ""}], "output_fields": [], "example_code": "import akshare as ak\nfortune_rank_df = ak.fortune_rank(year=\"2023\")\nprint(fortune_rank_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": null, "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": true, "tags": [], "analysts": []}