{"name": "movie_boxoffice_cinema_daily", "category": "others", "title": "", "description": "指定日期的每日各影院的票房数据", "target_address": "https://www.endata.com.cn/BoxOffice/BO/Cinema/day.html", "parameters": [{"name": "date", "type": "str", "description": "date=\"20240219\"; 输入具体的日期即可", "required": false, "default": ""}], "output_fields": [{"name": "排序", "type": "int64", "description": "票房排名"}, {"name": "影院名称", "type": "object", "description": ""}, {"name": "单日票房", "type": "float64", "description": "注意单位: 元"}, {"name": "单日场次", "type": "int64", "description": ""}, {"name": "场均人次", "type": "float64", "description": ""}, {"name": "场均票价", "type": "float64", "description": ""}, {"name": "上座率", "type": "float64", "description": "注意单位: %"}], "example_code": "import akshare as ak\nmovie_boxoffice_cinema_daily_df = ak.movie_boxoffice_cinema_daily(date=\"20240219\")\nprint(movie_boxoffice_cinema_daily_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": "day", "start_date": "20240219", "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}