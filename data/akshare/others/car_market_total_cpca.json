{"name": "car_market_total_cpca", "category": "others", "title": "", "description": "乘联会-统计数据-总体市场", "target_address": "http://data.cpcadata.com/TotalMarket", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"狭义乘用车\"; choice of {\"狭义乘用车\", \"广义乘用车\"}", "required": false, "default": ""}, {"name": "indicator", "type": "str", "description": "indicator=\"产量\"; choice of {\"产量\", \"批发\", \"零售\", \"出口\"}", "required": false, "default": ""}], "output_fields": [{"name": "月份", "type": "object", "description": ""}, {"name": "{前一个年份}年", "type": "float64", "description": "注意单位: 万辆"}, {"name": "{当前年份}年", "type": "float64", "description": "注意单位: 万辆"}], "example_code": "import akshare as ak\ncar_market_total_cpca_df = ak.car_market_total_cpca(symbol=\"狭义乘用车\", indicator=\"产量\")\nprint(car_market_total_cpca_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}