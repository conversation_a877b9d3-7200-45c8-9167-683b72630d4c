{"name": "car_market_fuel_cpca", "category": "others", "title": "", "description": "乘联会-统计数据-车型大类", "target_address": "http://data.cpcadata.com/FuelMarket", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"整体市场\"; choice of {\"整体市场\", \"销量占比-PHEV-BEV\", \"销量占比-ICE-NEV\"}", "required": false, "default": ""}], "output_fields": [{"name": "月份", "type": "object", "description": ""}, {"name": "{前一个年份}年", "type": "float64", "description": "注意单位: 万辆"}, {"name": "{当前年份}年", "type": "float64", "description": "注意单位: 万辆"}], "example_code": "import akshare as ak\ncar_market_fuel_cpca_df = ak.car_market_fuel_cpca()\nprint(car_market_fuel_cpca_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}