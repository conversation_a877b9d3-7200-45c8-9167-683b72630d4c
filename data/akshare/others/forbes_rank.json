{"name": "forbes_rank", "category": "others", "title": "", "description": "福布斯中国-榜单数据, 一共 87 个指标的数据可以获取", "target_address": "https://www.forbeschina.com/lists", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"2020年福布斯中国400富豪榜\"; 参考 福布斯中国指标一览表, 也可以访问 https://www.forbeschina.com/lists 获取所需要的 symbol", "required": false, "default": ""}, {"name": "福布斯中国指标一览表", "type": "name", "description": "url", "required": false, "default": ""}, {"name": "0", "type": "2020福布斯中国400富豪榜", "description": "https://www.forbeschina.com/lists/1750", "required": false, "default": ""}, {"name": "1", "type": "2020福布斯菲律宾富豪榜", "description": "https://www.forbeschina.com/lists/1746", "required": false, "default": ""}, {"name": "2", "type": "2020福布斯美国富豪榜", "description": "https://www.forbeschina.com/lists/1745", "required": false, "default": ""}, {"name": "3", "type": "2020福布斯中国名人榜", "description": "https://www.forbeschina.com/lists/1744", "required": false, "default": ""}, {"name": "4", "type": "2020福布斯新加坡富豪榜", "description": "https://www.forbeschina.com/lists/1743", "required": false, "default": ""}, {"name": "5", "type": "2020福布斯中国最佳CEO榜", "description": "https://www.forbeschina.com/lists/1741", "required": false, "default": ""}, {"name": "6", "type": "2020福布斯中国医疗健康富豪榜", "description": "https://www.forbeschina.com/lists/1740", "required": false, "default": ""}, {"name": "7", "type": "2020福布斯中国慈善榜", "description": "https://www.forbeschina.com/lists/1739", "required": false, "default": ""}, {"name": "8", "type": "2020福布斯韩国富豪榜", "description": "https://www.forbeschina.com/lists/1738", "required": false, "default": ""}, {"name": "9", "type": "2020福布斯中国科技女性榜", "description": "https://www.forbeschina.com/lists/1737", "required": false, "default": ""}, {"name": "10", "type": "2020福布斯全球亿万富豪榜中国子榜", "description": "https://www.forbeschina.com/lists/1734", "required": false, "default": ""}, {"name": "11", "type": "2020福布斯全球亿万富豪榜", "description": "https://www.forbeschina.com/lists/1733", "required": false, "default": ""}, {"name": "12", "type": "2019福布斯中国400富豪榜", "description": "https://www.forbeschina.com/lists/1728", "required": false, "default": ""}, {"name": "13", "type": "2019福布斯中国最佳创投人TOP100", "description": "https://www.forbeschina.com/lists/1747", "required": false, "default": ""}, {"name": "14", "type": "2019福布斯全球最有影响力体育经纪人", "description": "https://www.forbeschina.com/lists/1727", "required": false, "default": ""}, {"name": "15", "type": "2019福布斯中国30位30岁以下精英榜", "description": "https://www.forbeschina.com/lists/1725", "required": false, "default": ""}, {"name": "16", "type": "2019福布斯美国400富豪榜", "description": "https://www.forbeschina.com/lists/1722", "required": false, "default": ""}, {"name": "17", "type": "2019福布斯菲律宾富豪榜", "description": "https://www.forbeschina.com/lists/1721", "required": false, "default": ""}, {"name": "18", "type": "2019福布斯中国慈善榜", "description": "https://www.forbeschina.com/lists/1718", "required": false, "default": ""}, {"name": "19", "type": "2019福布斯100名人榜", "description": "https://www.forbeschina.com/lists/1717", "required": false, "default": ""}, {"name": "20", "type": "2019福布斯韩国富豪榜", "description": "https://www.forbeschina.com/lists/1716", "required": false, "default": ""}, {"name": "21", "type": "2019福布斯马来西亚50富豪榜", "description": "https://www.forbeschina.com/lists/19", "required": false, "default": ""}, {"name": "22", "type": "2019福布斯中国最杰出商界女性排行榜", "description": "https://www.forbeschina.com/lists/1165", "required": false, "default": ""}, {"name": "23", "type": "2019福布斯全球亿万富豪榜", "description": "https://www.forbeschina.com/lists/21", "required": false, "default": ""}, {"name": "24", "type": "2018福布斯中国商界25位潜力女性", "description": "https://www.forbeschina.com/lists/13", "required": false, "default": ""}, {"name": "25", "type": "2018福布斯中国慈善榜", "description": "https://www.forbeschina.com/lists/1156", "required": false, "default": ""}, {"name": "26", "type": "2018福布斯中国最佳创投人TOP100", "description": "https://www.forbeschina.com/lists/1258", "required": false, "default": ""}, {"name": "27", "type": "2018福布斯中国最富有女性Top25", "description": "https://www.forbeschina.com/lists/11", "required": false, "default": ""}, {"name": "28", "type": "2018福布斯中国最佳女性创投人TOP25", "description": "https://www.forbeschina.com/lists/12", "required": false, "default": ""}, {"name": "29", "type": "2018中国最杰出商界女性排行榜", "description": "https://www.forbeschina.com/lists/1145", "required": false, "default": ""}, {"name": "30", "type": "2018中国分析师最佳价值发现榜", "description": "https://www.forbeschina.com/lists/1147", "required": false, "default": ""}, {"name": "31", "type": "2018中国最佳分析师50强榜", "description": "https://www.forbeschina.com/lists/1148", "required": false, "default": ""}, {"name": "32", "type": "2018福布斯中国分析师最佳预测盈利能力榜", "description": "https://www.forbeschina.com/lists/1149", "required": false, "default": ""}, {"name": "33", "type": "2018全球亿万富豪榜", "description": "https://www.forbeschina.com/lists/1151", "required": false, "default": ""}, {"name": "34", "type": "2018福布斯中国30位30岁以下精英榜", "description": "https://www.forbeschina.com/lists/1157", "required": false, "default": ""}, {"name": "35", "type": "2018福布斯中国上市公司最佳CEO", "description": "https://www.forbeschina.com/lists/1159", "required": false, "default": ""}, {"name": "36", "type": "2018福布斯中国400富豪榜", "description": "https://www.forbeschina.com/lists/1162", "required": false, "default": ""}, {"name": "37", "type": "2017福布斯全球科技界100富豪榜", "description": "https://www.forbeschina.com/lists/1618", "required": false, "default": ""}, {"name": "38", "type": "2017福布斯中国30位30岁以下精英榜", "description": "https://www.forbeschina.com/lists/1617", "required": false, "default": ""}, {"name": "39", "type": "2017华人富豪榜", "description": "https://www.forbeschina.com/lists/1131", "required": false, "default": ""}, {"name": "40", "type": "2017全球亿万富豪榜", "description": "https://www.forbeschina.com/lists/1132", "required": false, "default": ""}, {"name": "41", "type": "2017福布斯全球运动员收入榜", "description": "https://www.forbeschina.com/lists/1644", "required": false, "default": ""}, {"name": "42", "type": "2017福布斯台湾50富豪榜", "description": "https://www.forbeschina.com/lists/1133", "required": false, "default": ""}, {"name": "43", "type": "2017福布斯中国上市公司最佳CEO", "description": "https://www.forbeschina.com/lists/1134", "required": false, "default": ""}, {"name": "44", "type": "2017福布斯中国名人榜", "description": "https://www.forbeschina.com/lists/1135", "required": false, "default": ""}, {"name": "45", "type": "2017中国慈善榜", "description": "https://www.forbeschina.com/lists/1681", "required": false, "default": ""}, {"name": "46", "type": "2017分析师最佳预测盈利能力榜", "description": "https://www.forbeschina.com/lists/1253", "required": false, "default": ""}, {"name": "47", "type": "2017福布斯中国最佳创投人TOP100", "description": "https://www.forbeschina.com/lists/1254", "required": false, "default": ""}, {"name": "48", "type": "2017中国最佳分析师50强榜", "description": "https://www.forbeschina.com/lists/1252", "required": false, "default": ""}, {"name": "49", "type": "2020年福布斯世界最佳雇主TOP100", "description": "https://www.forbeschina.com/lists/1749", "required": false, "default": ""}, {"name": "50", "type": "2020福布斯中国上市公司潜力企业榜", "description": "https://www.forbeschina.com/lists/1748", "required": false, "default": ""}, {"name": "51", "type": "2020福布斯亚州中小上市企业榜", "description": "https://www.forbeschina.com/lists/1742", "required": false, "default": ""}, {"name": "52", "type": "2020福布斯中国最具创新力企业榜", "description": "https://www.forbeschina.com/lists/1736", "required": false, "default": ""}, {"name": "53", "type": "2020福布斯全球企业2000强榜", "description": "https://www.forbeschina.com/lists/1735", "required": false, "default": ""}, {"name": "54", "type": "2019福布斯全球最具价值的体育经纪机构", "description": "https://www.forbeschina.com/lists/1726", "required": false, "default": ""}, {"name": "55", "type": "2019福布斯全球数字经济100强榜", "description": "https://www.forbeschina.com/lists/1724", "required": false, "default": ""}, {"name": "56", "type": "2019福布斯中国最具创新力企业榜", "description": "https://www.forbeschina.com/lists/1715", "required": false, "default": ""}, {"name": "57", "type": "2018福布斯中国新三板企业融资能力榜TOP50", "description": "https://www.forbeschina.com/lists/14", "required": false, "default": ""}, {"name": "58", "type": "2018福布斯中国最具创新力企业榜", "description": "https://www.forbeschina.com/lists/17", "required": false, "default": ""}, {"name": "59", "type": "2018非上市公司潜力企业榜", "description": "https://www.forbeschina.com/lists/18", "required": false, "default": ""}, {"name": "60", "type": "2018福布斯中国最佳创投机构", "description": "https://www.forbeschina.com/lists/20", "required": false, "default": ""}, {"name": "61", "type": "2018上市公司潜力企业榜", "description": "https://www.forbeschina.com/lists/1152", "required": false, "default": ""}, {"name": "62", "type": "2018福布斯中国新三板TOP100", "description": "https://www.forbeschina.com/lists/1155", "required": false, "default": ""}, {"name": "63", "type": "2018福布斯中国最佳PE机构", "description": "https://www.forbeschina.com/lists/1257", "required": false, "default": ""}, {"name": "64", "type": "2017福布斯中国家族企业", "description": "https://www.forbeschina.com/lists/1136", "required": false, "default": ""}, {"name": "65", "type": "2017福布斯全球企业2000强", "description": "https://www.forbeschina.com/lists/1139", "required": false, "default": ""}, {"name": "66", "type": "2017值得关注的新三板企业", "description": "https://www.forbeschina.com/lists/1459", "required": false, "default": ""}, {"name": "67", "type": "2017中国非上市公司潜力企业榜", "description": "https://www.forbeschina.com/lists/1460", "required": false, "default": ""}, {"name": "68", "type": "2017福布斯中国最佳PE机构", "description": "https://www.forbeschina.com/lists/1255", "required": false, "default": ""}, {"name": "69", "type": "2017福布斯中国最佳创投机构", "description": "https://www.forbeschina.com/lists/1256", "required": false, "default": ""}, {"name": "70", "type": "2019福布斯美国大学排行榜", "description": "https://www.forbeschina.com/lists/1720", "required": false, "default": ""}, {"name": "71", "type": "2018福布斯创新力最强的30个城市", "description": "https://www.forbeschina.com/lists/15", "required": false, "default": ""}, {"name": "72", "type": "2018福布斯最适合新生活的宜居城市", "description": "https://www.forbeschina.com/lists/16", "required": false, "default": ""}, {"name": "73", "type": "2018福布斯中国大陆最佳商业城市", "description": "https://www.forbeschina.com/lists/1163", "required": false, "default": ""}, {"name": "74", "type": "2017福布斯中国大陆最佳商业城市", "description": "https://www.forbeschina.com/lists/1138", "required": false, "default": ""}, {"name": "75", "type": "2017福布斯中国大陆最佳地级城市30强", "description": "https://www.forbeschina.com/lists/1140", "required": false, "default": ""}, {"name": "76", "type": "2017福布斯中国大陆最佳县级城市30强", "description": "https://www.forbeschina.com/lists/1141", "required": false, "default": ""}, {"name": "77", "type": "2017福布斯创新力最强的30个城市", "description": "https://www.forbeschina.com/lists/1142", "required": false, "default": ""}, {"name": "78", "type": "2017福布斯经营成本最高的30个城市", "description": "https://www.forbeschina.com/lists/1143", "required": false, "default": ""}, {"name": "79", "type": "2015福布斯全球最适宜经商的国家和地区", "description": "https://www.forbeschina.com/lists/1120", "required": false, "default": ""}, {"name": "80", "type": "2015美国最适宜经商和就业的城市", "description": "https://www.forbeschina.com/lists/1453", "required": false, "default": ""}, {"name": "81", "type": "2015美国就业增长最快城市100强", "description": "https://www.forbeschina.com/lists/1525", "required": false, "default": ""}, {"name": "82", "type": "2015美国最适合经商和就业的州", "description": "https://www.forbeschina.com/lists/1526", "required": false, "default": ""}, {"name": "83", "type": "2014美国最适宜经商和就业的地区", "description": "https://www.forbeschina.com/lists/1515", "required": false, "default": ""}, {"name": "84", "type": "2014福布斯美国最适合经商和就业的州", "description": "https://www.forbeschina.com/lists/1516", "required": false, "default": ""}, {"name": "85", "type": "2014年世界最负盛名城市榜", "description": "https://www.forbeschina.com/lists/1517", "required": false, "default": ""}, {"name": "86", "type": "2014福布斯全球最适宜经商的国家和地区", "description": "https://www.forbeschina.com/lists/1524", "required": false, "default": ""}], "output_fields": [{"name": "排名", "type": "str", "description": "根据不同的 symbol 而异"}], "example_code": "import akshare as ak\nforbes_rank_df = ak.forbes_rank(symbol=\"2020福布斯中国400富豪榜\")\nprint(forbes_rank_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}