{"name": "video_tv", "category": "others", "title": "", "description": "艺恩-视频放映-电视剧集", "target_address": "https://www.endata.com.cn/Video/index.html", "parameters": [], "output_fields": [{"name": "排序", "type": "int64", "description": ""}, {"name": "名称", "type": "object", "description": ""}, {"name": "类型", "type": "object", "description": ""}, {"name": "播映指数", "type": "float64", "description": ""}, {"name": "媒体热度", "type": "float64", "description": ""}, {"name": "用户热度", "type": "float64", "description": ""}, {"name": "好评度", "type": "float64", "description": ""}, {"name": "观看度", "type": "float64", "description": ""}, {"name": "统计日期", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nvideo_tv_df = ak.video_tv()\nprint(video_tv_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": "20250901", "start_year": "2025", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}