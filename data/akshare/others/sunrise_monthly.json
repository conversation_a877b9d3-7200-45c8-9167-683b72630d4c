{"name": "sunrise_monthly", "category": "others", "title": "", "description": "中国各大城市-日出和日落时间, 数据区间从 19990101-至今, 推荐使用代理访问", "target_address": "https://www.timeanddate.com/sun/china/", "parameters": [{"name": "date", "type": "str", "description": "date=\"20240428\"", "required": false, "default": ""}, {"name": "city", "type": "str", "description": "city=\"beijing\"; 注意输入的城市的拼音", "required": false, "default": ""}], "output_fields": [{"name": "date", "type": "object", "description": "日期-索引; XXXX-XX 格式"}, {"name": "feb", "type": "object", "description": "月份简称-随月份变化"}, {"name": "Sunrise", "type": "object", "description": "日出"}, {"name": "Sunset", "type": "object", "description": "日落"}, {"name": "Length", "type": "object", "description": "Daylength-Length"}, {"name": "Difference", "type": "object", "description": "Daylength-Difference"}, {"name": "Start", "type": "object", "description": "Astronomical Twilight-Start"}, {"name": "End", "type": "object", "description": "Astronomical Twilight-End"}, {"name": "Start.1", "type": "object", "description": "Nautical Twilight-Start"}, {"name": "End.1", "type": "object", "description": "Nautical Twilight-End"}, {"name": "Start.2", "type": "object", "description": "Civil Twilight-Start"}, {"name": "End.2", "type": "object", "description": "Civil Twilight-End"}, {"name": "Time", "type": "object", "description": "<PERSON> Noon-Time"}, {"name": "Mil. km", "type": "object", "description": "Solar Noon-Mil. km"}], "example_code": "import akshare as ak\nsunrise_monthly_df = ak.sunrise_monthly(date=\"20240428\", city=\"beijing\")\nprint(sunrise_monthly_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": "month", "start_date": "20240428", "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}