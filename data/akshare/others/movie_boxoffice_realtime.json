{"name": "movie_boxoffice_realtime", "category": "others", "title": "", "description": "当前时刻的实时电影票房数据, 每 5 分钟更新一次数据, 实时票房包含今天未开映场次已售出的票房", "target_address": "https://ys.endata.cn/BoxOffice/Movie", "parameters": [], "output_fields": [{"name": "排序", "type": "int64", "description": "票房排名"}, {"name": "影片名称", "type": "object", "description": ""}, {"name": "实时票房", "type": "float64", "description": "注意单位: 万"}, {"name": "票房占比", "type": "float64", "description": "注意单位: %"}, {"name": "上映天数", "type": "int64", "description": ""}, {"name": "累计票房", "type": "float64", "description": "注意单位: 万"}], "example_code": "import akshare as ak\nmovie_boxoffice_realtime_df = ak.movie_boxoffice_realtime()\nprint(movie_boxoffice_realtime_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}