{"name": "air_city_table", "category": "others", "title": "", "description": "所有能获取空气质量数据的城市表", "target_address": "https://www.aqistudy.cn/", "parameters": [], "output_fields": [{"name": "序号", "type": "int64", "description": ""}, {"name": "省份", "type": "object", "description": ""}, {"name": "城市", "type": "object", "description": ""}, {"name": "AQI", "type": "float64", "description": ""}, {"name": "空气质量", "type": "object", "description": ""}, {"name": "PM2.5浓度", "type": "object", "description": ""}, {"name": "首要污染物", "type": "object", "description": ""}], "example_code": "import akshare as ak\nair_city_table_df = ak.air_city_table()\nprint(air_city_table_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}