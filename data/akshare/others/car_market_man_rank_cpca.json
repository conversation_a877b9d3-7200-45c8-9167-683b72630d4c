{"name": "car_market_man_rank_cpca", "category": "others", "title": "", "description": "乘联会-统计数据-厂商排名", "target_address": "http://data.cpcadata.com/ManRank", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"狭义乘用车-单月\"; choice of {\"狭义乘用车-单月\", \"狭义乘用车-累计\", \"广义乘用车-单月\", \"广义乘用车-累计\"}", "required": false, "default": ""}, {"name": "indicator", "type": "str", "description": "indicator=\"批发\"; choice of {\"批发\", \"零售\"}", "required": false, "default": ""}], "output_fields": [{"name": "月份", "type": "object", "description": ""}, {"name": "{前一个年份}年", "type": "float64", "description": "注意单位: 万辆"}, {"name": "{当前年份}年", "type": "float64", "description": "注意单位: 万辆"}], "example_code": "import akshare as ak\ncar_market_man_rank_cpca_df = ak.car_market_man_rank_cpca(symbol=\"狭义乘用车-单月\", indicator=\"批发\")\nprint(car_market_man_rank_cpca_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": "xchg-code", "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}