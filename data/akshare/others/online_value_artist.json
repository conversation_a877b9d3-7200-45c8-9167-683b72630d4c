{"name": "online_value_artist", "category": "others", "title": "", "description": "艺恩-艺人-艺人流量价值", "target_address": "https://www.endata.com.cn/Marketing/Artist/business.html", "parameters": [], "output_fields": [{"name": "排名", "type": "int64", "description": ""}, {"name": "艺人", "type": "object", "description": ""}, {"name": "流量价值", "type": "float64", "description": "流量价值由专业度，关注度，预测热度，带货力加权汇总计算后得出，分值范围0~100，在商业价值的基础上增加了明星近期热度及带货力的权重。"}, {"name": "专业热度", "type": "float64", "description": "艺人专业热度主要表现艺人历史作品及品牌代言的效果情况，参与计算的指标维度包括历史主演电影票房表现，历史主演视频节目播映热度，电影作品豆瓣评分，作品相关微博内容评论正负向，历史代言品牌数量，品牌热度，艺人获奖数量。"}, {"name": "关注热度", "type": "float64", "description": "艺人关注热度主要表现艺人网络中的舆情声量，参与计算的指标维度包括百度搜索指数，百度新闻数量，今日头条新闻数，微博转发量，微博评论量，微博点赞量，微博粉丝数量，贴吧关注数量，微博话题数量。"}, {"name": "预测热度", "type": "float64", "description": "预测热度的数值反映明星的未来发展潜力，包括粉丝增长规模，作品口碑以及未来作品预测。"}, {"name": "带货力", "type": "float64", "description": "带货力的数值代表艺人的带货号召力，包括艺人的铁杆粉丝规模，超话粉丝规模。"}, {"name": "统计日期", "type": "object", "description": ""}], "example_code": "import akshare as ak\nonline_value_artist_df = ak.online_value_artist()\nprint(online_value_artist_df)", "source_url": "https://akshare.akfamily.xyz/data/others/others.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": "20250902", "start_year": "2025", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}