{"name": "index_analysis_monthly_sw", "category": "index", "title": "", "description": "申万宏源研究-指数分析-月报表", "target_address": "https://www.swsresearch.com/institute_sw/allIndex/analysisIndex", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"市场表征\"; choice of {\"市场表征\", \"一级行业\", \"二级行业\", \"风格指数\"}", "required": false, "default": ""}, {"name": "date", "type": "str", "description": "start_date=\"20221031\"; 通过调用 ak.index_analysis_week_month_sw(date=\"month\") 接口获取", "required": false, "default": ""}], "output_fields": [{"name": "指数代码", "type": "object", "description": ""}, {"name": "指数名称", "type": "object", "description": ""}, {"name": "发布日期", "type": "object", "description": ""}, {"name": "收盘指数", "type": "float64", "description": ""}, {"name": "成交量", "type": "float64", "description": "注意单位: 亿股"}, {"name": "涨跌幅", "type": "float64", "description": "注意单位: %"}, {"name": "换手率", "type": "float64", "description": "注意单位: %"}, {"name": "市盈率", "type": "float64", "description": "注意单位: 倍"}, {"name": "市净率", "type": "float64", "description": "注意单位: 倍"}, {"name": "均价", "type": "float64", "description": "注意单位: 元"}, {"name": "成交额占比", "type": "float64", "description": "注意单位: %"}, {"name": "流通市值", "type": "float64", "description": "注意单位: 亿元"}, {"name": "平均流通市值", "type": "float64", "description": "注意单位: 亿元"}, {"name": "股息率", "type": "float64", "description": "注意单位: %"}], "example_code": "import akshare as ak\nindex_analysis_monthly_sw_df = ak.index_analysis_monthly_sw(symbol=\"市场表征\", date=\"20240930\")\nprint(index_analysis_monthly_sw_df)", "source_url": "https://akshare.akfamily.xyz/data/index/index.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": "month", "start_date": "20240930", "start_year": "2024", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}