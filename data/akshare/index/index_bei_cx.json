{"name": "index_bei_cx", "category": "index", "title": "", "description": "财新指数-基石经济指数", "target_address": "https://yun.ccxe.com.cn/indices/bei", "parameters": [], "output_fields": [{"name": "日期", "type": "object", "description": ""}, {"name": "基石经济指数", "type": "float64", "description": ""}, {"name": "变化幅度", "type": "float64", "description": "注意单位: %"}], "example_code": "import akshare as ak\nindex_bei_cx_df = ak.index_bei_cx()\nprint(index_bei_cx_df)", "source_url": "https://akshare.akfamily.xyz/data/index/index.html", "symbol_format": null, "time_type": "time_series", "time_interval": "day", "start_date": "20161226", "start_year": "2016", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}