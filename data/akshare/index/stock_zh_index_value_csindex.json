{"name": "stock_zh_index_value_csindex", "category": "index", "title": "", "description": "中证指数-指数估值数据", "target_address": "https://www.csindex.com.cn/zh-CN/indices/index-detail/H30374#/indices/family/detail?indexCode=H30374", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"H30374\"; 指数代码", "required": false, "default": ""}], "output_fields": [{"name": "日期", "type": "object", "description": ""}, {"name": "指数代码", "type": "object", "description": ""}, {"name": "指数中文全称", "type": "object", "description": ""}, {"name": "指数中文简称", "type": "object", "description": ""}, {"name": "指数英文全称", "type": "object", "description": ""}, {"name": "指数英文简称", "type": "object", "description": ""}, {"name": "市盈率1", "type": "float64", "description": "注意: （总股本）P/E1"}, {"name": "市盈率2", "type": "float64", "description": "注意: （计算用股本）P/E2"}, {"name": "股息率1", "type": "float64", "description": "注意: （总股本）D/P1"}, {"name": "股息率2", "type": "float64", "description": "注意: （计算用股本）D/P2"}], "example_code": "import akshare as ak\nstock_zh_index_value_csindex_df = ak.stock_zh_index_value_csindex(symbol=\"H30374\")\nprint(stock_zh_index_value_csindex_df)", "source_url": "https://akshare.akfamily.xyz/data/index/index.html", "symbol_format": null, "time_type": "time_series", "time_interval": "day", "start_date": "20250806", "start_year": "2025", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}