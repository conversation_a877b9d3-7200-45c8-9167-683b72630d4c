{"name": "index_min_sw", "category": "index", "title": "", "description": "申万宏源研究-指数发布-指数详情-指数分时数据", "target_address": "https://www.swsresearch.com//institute_sw/allIndex/releasedIndex/releasedetail?code=801001&name=申万中小", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"801030\"; 指数代码", "required": false, "default": ""}], "output_fields": [{"name": "代码", "type": "object", "description": ""}, {"name": "名称", "type": "object", "description": ""}, {"name": "价格", "type": "float64", "description": ""}, {"name": "日期", "type": "object", "description": ""}, {"name": "时间", "type": "object", "description": ""}], "example_code": "import akshare as ak\nindex_min_sw_df = ak.index_min_sw(symbol=\"801001\")\nprint(index_min_sw_df)", "source_url": "https://akshare.akfamily.xyz/data/index/index.html", "symbol_format": "code_only", "time_type": "cross_sectional", "time_interval": null, "start_date": "20250903", "start_year": "2025", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}