{"name": "index_hist_sw", "category": "index", "title": "", "description": "申万宏源研究-指数发布-指数详情-指数历史数据", "target_address": "https://www.swsresearch.com//institute_sw/allIndex/releasedIndex/releasedetail?code=801002&name=申万中小", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"801030\"; 指数代码", "required": false, "default": ""}, {"name": "period", "type": "str", "description": "period=\"day\"; choice of {\"day\", \"week\", \"month\"}", "required": false, "default": ""}], "output_fields": [{"name": "代码", "type": "object", "description": ""}, {"name": "日期", "type": "object", "description": ""}, {"name": "收盘", "type": "float64", "description": ""}, {"name": "开盘", "type": "float64", "description": ""}, {"name": "最高", "type": "float64", "description": ""}, {"name": "最低", "type": "float64", "description": ""}, {"name": "成交量", "type": "float64", "description": ""}, {"name": "成交额", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nindex_hist_sw_df = ak.index_hist_sw(symbol=\"801193\", period=\"day\")\nprint(index_hist_sw_df)", "source_url": "https://akshare.akfamily.xyz/data/index/index.html", "symbol_format": "code_only", "time_type": "time_series", "time_interval": "day", "start_date": "19991230", "start_year": "1999", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}