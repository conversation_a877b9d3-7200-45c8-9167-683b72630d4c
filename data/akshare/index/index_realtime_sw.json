{"name": "index_realtime_sw", "category": "index", "title": "", "description": "申万宏源研究-指数系列; 注意其中大类风格指数和金创指数的字段", "target_address": "https://www.swsresearch.com/institute_sw/allIndex/releasedIndex", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"市场表征\"; choice of {\"市场表征\", \"一级行业\", \"二级行业\", \"风格指数\", \"大类风格指数\", \"金创指数\"}", "required": false, "default": ""}], "output_fields": [{"name": "指数代码", "type": "object", "description": ""}, {"name": "指数名称", "type": "object", "description": ""}, {"name": "昨收盘", "type": "float64", "description": ""}, {"name": "今开盘", "type": "float64", "description": ""}, {"name": "最新价", "type": "float64", "description": ""}, {"name": "成交额", "type": "float64", "description": "注意: 百万元"}, {"name": "成交量", "type": "float64", "description": "注意: 百万股"}, {"name": "最高价", "type": "float64", "description": ""}, {"name": "最低价", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nindex_realtime_sw_df = ak.index_realtime_sw(symbol=\"市场表征\")\nprint(index_realtime_sw_df)", "source_url": "https://akshare.akfamily.xyz/data/index/index.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}