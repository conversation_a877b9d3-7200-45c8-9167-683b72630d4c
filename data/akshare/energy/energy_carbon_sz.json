{"name": "energy_carbon_sz", "category": "energy", "title": "", "description": "深圳碳排放交易所-国内碳情", "target_address": "http://www.cerx.cn/dailynewsCN/index.htm", "parameters": [], "output_fields": [{"name": "交易日期", "type": "object", "description": ""}, {"name": "市场交易指数", "type": "object", "description": ""}, {"name": "开盘价", "type": "float64", "description": ""}, {"name": "最高价", "type": "float64", "description": ""}, {"name": "最低价", "type": "float64", "description": ""}, {"name": "成交均价", "type": "float64", "description": ""}, {"name": "收盘价", "type": "float64", "description": ""}, {"name": "成交量", "type": "int64", "description": ""}, {"name": "成交额", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nenergy_carbon_sz_df = ak.energy_carbon_sz()\nprint(energy_carbon_sz_df)", "source_url": "https://akshare.akfamily.xyz/data/energy/energy.html", "symbol_format": null, "time_type": null, "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": true, "tags": [], "analysts": []}