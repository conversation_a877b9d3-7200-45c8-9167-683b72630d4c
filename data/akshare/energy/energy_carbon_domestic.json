{"name": "energy_carbon_domestic", "category": "energy", "title": "", "description": "碳交易网-行情信息", "target_address": "http://www.tanjiaoyi.com/", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"湖北\"; choice of {'湖北', '上海', '北京', '重庆', '广东', '天津', '深圳', '福建'}", "required": false, "default": ""}], "output_fields": [{"name": "日期", "type": "object", "description": ""}, {"name": "成交价", "type": "float64", "description": "注意单位: 元"}, {"name": "成交量", "type": "float64", "description": "注意单位: 吨"}, {"name": "成交额", "type": "float64", "description": ""}, {"name": "地点", "type": "object", "description": ""}], "example_code": "import akshare as ak\nenergy_carbon_domestic_df = ak.energy_carbon_domestic(symbol=\"湖北\")\nprint(energy_carbon_domestic_df)", "source_url": "https://akshare.akfamily.xyz/data/energy/energy.html", "symbol_format": null, "time_type": "time_series", "time_interval": "day", "start_date": "20140402", "start_year": "2014", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}