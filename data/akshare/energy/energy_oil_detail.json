{"name": "energy_oil_detail", "category": "energy", "title": "", "description": "东方财富-数据中心-中国油价-地区油价", "target_address": "https://data.eastmoney.com/cjsj/oil_default.html", "parameters": [{"name": "date", "type": "str", "description": "date=\"20200319\"; 此日期为调价日期, 通过调用 ak.energy_oil_hist() 可以获取历史调价日期", "required": false, "default": ""}], "output_fields": [{"name": "日期", "type": "object", "description": "价格调整的日期"}, {"name": "地区", "type": "object", "description": "地区"}, {"name": "V_0", "type": "float64", "description": "0#柴油价格(单位:元/升)"}, {"name": "V_92", "type": "float64", "description": "92#汽油价格(单位:元/升)"}, {"name": "V_95", "type": "float64", "description": "95#汽油价格(单位:元/升)"}, {"name": "V_89", "type": "float64", "description": "89#汽油价格(单位:元/升)"}, {"name": "ZDE_0", "type": "float64", "description": "0#柴油涨幅(单位:元/升)"}, {"name": "ZDE_92", "type": "float64", "description": "92#汽油涨幅(单位:元/升)"}, {"name": "ZDE_95", "type": "float64", "description": "95#汽油涨幅(单位:元/升)"}, {"name": "ZDE_89", "type": "float64", "description": "89#汽油涨幅(单位:元/升)"}, {"name": "QE_0", "type": "float64", "description": "上一次调整时0#柴油价格(单位:元/升)"}, {"name": "QE_92", "type": "float64", "description": "上一次调整时92#汽油价格(单位:元/升)"}, {"name": "QE_95", "type": "float64", "description": "上一次调整时95#汽油价格(单位:元/升)"}, {"name": "QE_89", "type": "float64", "description": "上一次调整时89#汽油价格(单位:元/升)"}], "example_code": "import akshare as ak\nenergy_oil_detail_df = ak.energy_oil_detail(date=\"20240118\")\nprint(energy_oil_detail_df)", "source_url": "https://akshare.akfamily.xyz/data/energy/energy.html", "symbol_format": null, "time_type": "cross_sectional", "time_interval": null, "start_date": "20240118", "start_year": "2024", "empty_result": false, "runtime_error": false, "tags": [], "analysts": []}