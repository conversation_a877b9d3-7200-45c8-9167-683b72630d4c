{"name": "energy_carbon_gz", "category": "energy", "title": "", "description": "广州碳排放权交易中心-行情信息", "target_address": "http://www.cnemission.com/article/hqxx/", "parameters": [], "output_fields": [{"name": "日期", "type": "object", "description": ""}, {"name": "品种", "type": "object", "description": ""}, {"name": "开盘价", "type": "float64", "description": ""}, {"name": "收盘价", "type": "float64", "description": ""}, {"name": "最高价", "type": "float64", "description": ""}, {"name": "最低价", "type": "float64", "description": ""}, {"name": "涨跌", "type": "float64", "description": ""}, {"name": "涨跌幅", "type": "float64", "description": "注意单位: %"}, {"name": "成交数量", "type": "int64", "description": ""}, {"name": "成交金额", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nenergy_carbon_gz_df = ak.energy_carbon_gz()\nprint(energy_carbon_gz_df)", "source_url": "https://akshare.akfamily.xyz/data/energy/energy.html", "symbol_format": null, "time_type": null, "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": true, "tags": [], "analysts": []}