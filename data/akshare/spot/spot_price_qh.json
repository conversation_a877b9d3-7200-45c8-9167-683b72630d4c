{"name": "spot_price_qh", "category": "spot", "title": "", "description": "99 期货-数据-期现-现货走势", "target_address": "https://www.99qh.com/data/spotTrend", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"螺纹钢\"; 可以通过 ak.spot_price_table_qh() 获取品种表", "required": false, "default": ""}], "output_fields": [{"name": "日期", "type": "object", "description": ""}, {"name": "期货收盘价", "type": "float64", "description": ""}, {"name": "现货价格", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nspot_price_qh_df = ak.spot_price_qh(symbol='螺纹钢')\nprint(spot_price_qh_df)", "source_url": "https://akshare.akfamily.xyz/data/spot/spot.html", "symbol_format": null, "time_type": null, "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": true, "tags": [], "analysts": []}