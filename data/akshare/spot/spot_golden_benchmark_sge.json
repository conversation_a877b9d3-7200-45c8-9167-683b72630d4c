{"name": "spot_golden_benchmark_sge", "category": "spot", "title": "", "description": "上海黄金交易所-数据资讯-上海金基准价-历史数据", "target_address": "https://www.sge.com.cn/sjzx/jzj", "parameters": [], "output_fields": [{"name": "交易时间", "type": "object", "description": ""}, {"name": "晚盘价", "type": "float64", "description": ""}, {"name": "早盘价", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nspot_golden_benchmark_sge_df = ak.spot_golden_benchmark_sge()\nprint(spot_golden_benchmark_sge_df)", "source_url": "https://akshare.akfamily.xyz/data/spot/spot.html", "symbol_format": null, "time_type": null, "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": true, "tags": [], "analysts": []}