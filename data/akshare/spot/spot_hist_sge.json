{"name": "spot_hist_sge", "category": "spot", "title": "", "description": "上海黄金交易所-数据资讯-行情走势-历史数据", "target_address": "https://www.sge.com.cn/sjzx/mrhq", "parameters": [{"name": "symbol", "type": "str", "description": "symbol=\"Au99.99\"; 可以通过 ak.spot_symbol_table_sge() 获取品种表", "required": false, "default": ""}], "output_fields": [{"name": "date", "type": "object", "description": ""}, {"name": "open", "type": "float64", "description": ""}, {"name": "close", "type": "float64", "description": ""}, {"name": "low", "type": "float64", "description": ""}, {"name": "high", "type": "float64", "description": ""}], "example_code": "import akshare as ak\nspot_hist_sge_df = ak.spot_hist_sge(symbol='Au99.99')\nprint(spot_hist_sge_df)", "source_url": "https://akshare.akfamily.xyz/data/spot/spot.html", "symbol_format": null, "time_type": null, "time_interval": null, "start_date": null, "start_year": null, "empty_result": false, "runtime_error": true, "tags": [], "analysts": []}