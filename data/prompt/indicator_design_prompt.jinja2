# AkShare API 金融指标生成提示词

## 任务概述
基于 AkShare API 定义和实际数据样本，为金融投资分析生成有价值的量化指标。

**API名称**: {{ api_name }}
**数据分类**: {{ category }}
**目标日期**: {{ target_date }}（最近交易日）

## 执行流程

**请一步一步深入思考。**

### 第一步：创建目标目录
**重要**：开始处理的第一步必须创建目标目录 `src/finance_agent/function/{{ category }}/{{ api_name }}`，这样即使提前结束，也会被视为已经作出判断和处理，避免重复处理同一API。

### 第二步：API 价值评估

#### API 基本信息
- **API名称**: `ak.{{ api_name }}`
- **功能描述**: {{ api_definition.description }}
- **数据来源**: {{ api_definition.target_address or '未知' }}

#### 输入参数分析
{% if api_definition.parameters %}
**输入参数**:
{% for param in api_definition.parameters %}
- `{{ param.name }}` ({{ param.type }}): {{ param.description }}{% if param.required %} [必需]{% endif %}
{% endfor %}
{% else %}
**输入参数**: 无参数
{% endif %}

{% if api_definition.output_fields %}
#### API定义的输出字段
**输出字段** ({{ api_definition.output_fields|length }} 个字段):
{% for field in api_definition.output_fields %}
- `{{ field.name }}` ({{ field.type }}): {{ field.description or '无描述' }}
{% endfor %}
{% endif %}

#### 实际数据展示

**数据规模**: {{ data_info.row_count }} 行 × {{ data_info.column_count }} 列

**实际数据样本**（前{{ data_info.sample_rows }}行）:
```
{{ data_info.data_sample }}
```

**字段信息**:
{% for column in data_info.columns %}
- `{{ column.name }}` ({{ column.dtype }}): 样本值 {{ column.sample_values|join(', ') }}
{% endfor %}

**数据质量评估**:
- 数据完整性: {{ data_info.completeness }}%
- 缺失值字段数: {{ data_info.missing_values }}
- 数值字段数量: {{ data_info.numeric_columns }}
- 文本字段数量: {{ data_info.text_columns }}
- 日期字段数量: {{ data_info.date_columns }}

#### 投资价值判断
请根据以下标准评估该 API 获取的数据是否能用于投资决策和分析：

**评估标准**：
- 数据是否对投资决策有参考价值（价格、成交量、财务指标、宏观经济数据、市场情绪数据等）
- 数据频率是否适合投资分析（日频、周频、月频、季频、年频等）
- 数据覆盖范围是否足够进行有意义的分析
- 数据是否能够用于构建投资指标或辅助投资判断

**排除标准**：去除与投资无直接关联的API，如：
- 交易日历、市场规则、制度性信息
- 新股上市公告、交易所公告等纯信息类数据
- 市场基础设施、技术规范等非投资数据

**如果判断无投资价值，提早结束当前任务，跳到最后一步**

### 第三步：分析师角色匹配

首先基于上述实际数据样本，深入分析数据特征：
- 识别关键的投资分析要素（价格、成交量、财务指标、宏观数据等）
- 评估数据的时间序列特征和更新频率
- 分析数据的完整性、准确性和可用性
- 确定数据适合的分析维度和指标构建方向

然后根据数据特征分析结果，从以下专业分析师中选择 1-3 个最适合的角色：

#### 可选分析师角色

**1. 技术分析师 (Technical Analyst)**
- **专业领域**：价格走势分析、技术指标构建、图表形态识别
- **数据需求**：开盘价、收盘价、最高价、最低价、成交量
- **分析重点**：趋势判断、支撑阻力、买卖信号、动量分析
- **适用场景**：包含完整 OHLCV 数据的 API

**2. 量化分析师 (Quantitative Analyst)**
- **专业领域**：数学建模、统计分析、风险量化
- **数据需求**：价格序列、收益率数据、波动率信息
- **分析重点**：收益率计算、风险度量、相关性分析、因子建模
- **适用场景**：具有时间序列特征的价格数据

**3. 市场微观结构分析师 (Market Microstructure Analyst)**
- **专业领域**：交易行为分析、流动性研究、市场效率评估
- **数据需求**：成交量、成交额、换手率、买卖价差
- **分析重点**：流动性指标、交易活跃度、价格发现效率
- **适用场景**：包含交易量和流动性数据的 API

**4. 行为金融分析师 (Behavioral Finance Analyst)**
- **专业领域**：投资者情绪分析、市场异象识别、行为偏差研究
- **数据需求**：价格波动、异常交易、情绪代理变量
- **分析重点**：情绪指标、异常检测、行为模式识别
- **适用场景**：包含波动率、异常交易数据的 API

**5. 基本面分析师 (Fundamental Analyst)**
- **专业领域**：财务分析、估值建模、行业比较
- **数据需求**：财务报表、估值指标、行业数据
- **分析重点**：财务比率、估值水平、盈利质量
- **适用场景**：包含财务或估值数据的 API

**6. 宏观经济分析师 (Macro Economist)**
- **专业领域**：宏观经济分析、政策影响评估、周期研究
- **数据需求**：宏观经济指标、政策数据、周期性数据
- **分析重点**：经济周期、政策影响、宏观趋势
- **适用场景**：包含宏观经济数据的 API

**7. 新闻舆情分析师 (News Sentiment Analyst)**
- **专业领域**：新闻分析、舆情监测、事件影响评估
- **数据需求**：新闻文本、舆情数据、事件数据
- **分析重点**：情绪分析、事件影响、舆论趋势
- **适用场景**：包含新闻或文本数据的 API

**排除条件**：
- 如果数据属于另类数据（如ESG评级、院线票房、天气数据等）且无法匹配合适的分析师角色，提早结束当前任务，跳到最后一步
- 如果数据质量过低、覆盖范围不足或时效性差，无法支撑有效的投资分析，提早结束当前任务，跳到最后一步

### 第四步：指标设计
为每个选定的分析师设计 3-5 个专业投资分析指标：

#### 指标设计原则
1. **专业性**：指标应严格符合该分析师的专业领域和分析方法论
2. **实用性**：指标应对实际投资决策具有明确的指导价值和可操作性
3. **可计算性**：基于API提供的数据字段能够准确、稳定地计算
4. **差异化**：不同分析师的指标应体现各自专业特色，避免重复

#### 指标设计要求
- **指标名称**：简洁明确，准确体现指标的核心含义和用途
- **计算公式**：提供详细的数学公式，明确指出具体使用哪些数据字段
- **业务含义**：阐述指标在投资分析中的意义、应用场景和决策价值
- **数值范围**：说明指标的正常取值范围、异常值判断标准和风险阈值

#### 输出格式要求
请严格按照以下格式输出完整的分析报告：

```markdown
# API 分析报告：{{ api_name }}

## 1. API 价值评估
- **功能描述**：[详细描述API的核心功能和数据内容]
- **投资价值**：[明确评估该API数据的投资分析价值]
- **适用场景**：[列出具体的投资分析应用场景]

## 2. 分析师匹配
- **选定分析师**：[列出1-3个最适合的分析师角色]
- **匹配理由**：[详细说明选择这些分析师的数据依据和逻辑]

## 3. 数据特征分析
- **数据结构**：[描述数据的时间序列特征、更新频率等结构特点]
- **关键字段**：[列出对投资分析最重要的数据字段]
- **数据质量**：[评估数据的完整性、准确性和时效性]

## 4. 指标设计

### [分析师名称]专业指标

1. **[指标名称]**
   - 计算公式：[提供具体的数学公式和字段映射]
   - 业务含义：[阐述指标的投资分析意义和应用价值]
   - 数值范围：[说明正常范围、异常值标准和风险阈值]

[为每个选定的分析师重复上述指标设计格式]
```

### 最后一步：工具调用选择

根据前面的分析结果，选择合适的工具进行后续处理：

#### 情况一：提前结束（无投资价值或无法匹配分析师）
如果在第二步投资价值判断中认为该API数据无投资分析价值，或在第三步中无法匹配到合适的分析师角色，则直接调用：
```
generate_indicator_design_prompt_mcp()
```
继续处理下一个API的指标设计。

#### 情况二：成功完成指标设计
如果成功完成了完整的指标设计，为选定的分析师生成了具体的投资分析指标，则调用：
```
generate_indicator_coder_prompt_mcp()
```
此工具将为设计好的指标进行Python代码实现和测试。

### 重要提醒
**必须再次调用工具**：无论是哪种情况，都必须调用相应的工具继续开发流程。任何情况下不能自行中止开发流程，必须通过工具调用来推进到下一个阶段。
