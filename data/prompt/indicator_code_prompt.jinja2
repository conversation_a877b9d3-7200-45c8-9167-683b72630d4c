# AkShare API 金融指标编程实现提示词

## 任务概述
基于已设计的投资分析指标，为金融投资分析编写Python函数实现代码。

**API名称**: {{ api_name }}
**数据分类**: {{ category }}
**目标日期**: {{ target_date }}（最近交易日）

## 执行流程

**请一步一步深入思考，严格按照以下步骤执行。**

### 第一步：API价值评估

#### API基本信息
- **API名称**: `ak.{{ api_name }}`
- **功能描述**: {{ api_definition.description }}
- **数据来源**: {{ api_definition.target_address or '未知' }}

#### 输入参数分析
{% if api_definition.parameters %}
**输入参数**:
{% for param in api_definition.parameters %}
- `{{ param.name }}` ({{ param.type }}): {{ param.description }}{% if param.required %} [必需]{% endif %}
{% endfor %}
{% else %}
**输入参数**: 无参数
{% endif %}

{% if api_definition.output_fields %}
#### API定义的输出字段
**输出字段** ({{ api_definition.output_fields|length }} 个字段):
{% for field in api_definition.output_fields %}
- `{{ field.name }}` ({{ field.type }}): {{ field.description or '无描述' }}
{% endfor %}
{% endif %}

#### 实际数据分析

**数据规模**: {{ data_info.row_count }} 行 × {{ data_info.column_count }} 列

**实际数据样本**（前{{ data_info.sample_rows }}行）:
```
{{ data_info.data_sample }}
```

**字段信息**:
{% for column in data_info.columns %}
- `{{ column.name }}` ({{ column.dtype }}): 样本值 {{ column.sample_values|join(', ') }}
{% endfor %}

### 第二步：指标设计回顾

基于之前的指标设计结果，确认需要实现的指标：

{% for analyst_type, analyst_indicators in indicators.items %}

#### {{ analyst_type }}分析师指标
{% for indicator in analyst_indicators %}
**{{ loop.index }}. {{ indicator.name }}**
- **计算公式**：{{ indicator.formula }}
- **业务含义**：{{ indicator.business_meaning }}
- **数值范围**：{{ indicator.value_range }}

{% endfor %}
{% endfor %}

### 第三步：函数实现

**工作内容**：
- 创建代码文件并实现指标计算函数
- 处理API调用和数据预处理
- 实现具体的指标计算逻辑

**目标文件路径**：`src/finance_agent/function/{{ category }}/{{ api_name }}/indicators.py`

**可用的第三方库**：
1. **akshare**：用于获取金融数据（必需）
2. **pandas**：优先使用pandas的现成方法进行数据处理、统计计算和时间序列分析（必需）
3. **numpy**：用于数值计算、数组操作和数学函数（按需导入）
4. **scipy**：用于高级统计分析、信号处理和科学计算（如scipy.stats）（按需导入）
5. **scikit-learn**：用于机器学习相关的预处理、特征工程和模型评估（按需导入）

**重要提醒**：只导入实际使用的库，避免导入警告。对于可选库（scipy、scikit-learn），在代码模板中已注释，需要时再取消注释。

**函数命名规范**：
- 函数名格式：`{analyst_type}_indicators`
- 例如：`technical_indicators`, `quantitative_indicators`

**参数转换处理**：
- **股票代码转换**：`600000.SH` → `600000`（根据API要求）
- **日期格式转换**：`2025-01-01` → `20250101`（根据API要求）
- **API参数设置**：设定合理的默认参数值（复权方式、时间间隔等）
- **参数验证**：在API调用前验证所有参数格式

**数据获取策略**：
- 全量数据API：根据symbol参数过滤目标股票数据
- 市场数据API：保持symbol参数但内部可忽略
- 行业数据API：根据股票所属行业进行数据匹配

**数据预处理**：
- 数据清洗和格式化
- 缺失值处理
- 数据类型转换
- 时间序列对齐

**计算逻辑**：
- 根据指标设计方案实现具体计算
- 使用pandas、numpy等库进行高效计算
- 处理边界情况和异常值
- 确保计算结果的准确性

**返回格式规范**：
```python
{
    "指标名称1": 计算结果1,
    "指标名称2": 计算结果2,
    "指标名称3": 计算结果3,
    "计算日期": end_date,
    "股票代码": symbol,
    "数据点数": len(df),
    "回看天数": lookback_days,
    "市场类型": market_type
}
```

**完整文件结构模板**：
```python
"""
{{ api_name }} 指标计算函数

基于 akshare API: ak.{{ api_name }}
数据来源: {{ api_definition.target_address or '未知' }}
"""

# 导入必要的库（只导入实际使用的库，避免警告）
import akshare as ak
import pandas as pd

{% for analyst_type, analyst_indicators in indicators.items %}
def {{ analyst_type }}_indicators(symbol: str, end_date: str, **kwargs) -> dict[str, Any]:
    """
    {{ analyst_type }}分析师指标计算函数

    标准函数签名：
    Args:
        symbol: 股票代码，格式为 "600000.SH"（上海）或 "000001.SZ"（深圳）
        end_date: 计算截止日期，格式为 "YYYY-MM-DD"
        **kwargs: 其他可选参数（都有默认值）
            - lookback_days: 回看天数，默认252
            - market_type: 市场类型，默认"全部A股"

    Returns:
        dict[str, Any]: 包含指标名称和计算结果的字典
    """

    # 参数处理
    lookback_days = kwargs.get('lookback_days', 252)
    market_type = kwargs.get('market_type', '全部A股')

    # 代码和日期格式转换
    # symbol_converted = symbol.split('.')[0]  # 示例转换：600000.SH → 600000
    # end_date_converted = end_date.replace('-', '')  # 示例转换：2025-01-01 → 20250101

    # API调用
    try:
        df = ak.{{ api_name }}(参数...)  # 根据API要求填入正确参数
    except Exception as e:
        return {
            "error": f"API调用失败: {str(e)}",
            "计算日期": end_date,
            "股票代码": symbol
        }

    # 数据预处理和过滤
    # 根据symbol和end_date过滤数据
    # 数据清洗和格式化
    # 缺失值处理
    # 数据类型转换
    # 时间序列对齐

    # 指标计算
    indicators = {}

    {% for indicator in analyst_indicators %}
    # {{ indicator.name }}
    # 计算公式：{{ indicator.formula }}
    # 业务含义：{{ indicator.business_meaning }}
    # 数值范围：{{ indicator.value_range }}
    indicators["{{ indicator.name }}"] = 0.0  # 替换为实际计算逻辑

    {% endfor %}
    # 添加元数据
    indicators.update({
        "计算日期": end_date,
        "股票代码": symbol,
        "数据点数": len(df),
        "回看天数": lookback_days,
        "市场类型": market_type
    })

    return indicators

{% endfor %}
# 测试代码
if __name__ == "__main__":
    # 测试参数
    test_symbol = "000001.SZ"  # 平安银行
    test_date = "2024-12-31"

    # 测试每个指标函数
    {% for analyst_type, analyst_indicators in indicators.items %}
    print("=== {{ analyst_type }}指标测试 ===")
    result = {{ analyst_type }}_indicators(test_symbol, test_date)
    print(result)
    print()
    {% endfor %}
```

#### 注意事项

**严格禁止的行为**：
1. **禁止编写防御性代码掩盖真实错误**：
   - 不得使用过度的try/except来隐藏错误
   - 不得在出现异常时返回默认值或空值来掩盖问题
   - 不得使用if/else来避免可能的错误而不处理根本原因
   - 错误应该暴露出来，便于调试和修复

2. **严禁在代码内写入任何模拟数据**：
   - 不得使用硬编码的数值作为指标计算结果
   - 不得使用随机数或固定值来模拟API返回数据
   - 不得使用示例数据来替代真实的API调用
   - 所有数据必须来自真实的API调用和计算

**正确的编程原则**：
- 让错误自然暴露，便于发现和解决问题
- 所有计算结果必须基于真实数据
- 代码应该在正确的情况下工作，在错误的情况下明确失败
- 优先保证代码的正确性，而不是表面的"健壮性"

### 第四步：测试与修复

**工作内容**：
- 执行函数测试验证功能正确性
- 根据错误信息进行迭代修复
- 验证计算结果的合理性
- 处理网络和数据问题

**可用的分析工具**：
- `uv run ruff`：代码风格检查和自动修复
- `uv run mypy`：静态类型检查
- `uv run pyright`：高级类型分析
- 如果存在警告应确认了解原因，不得忽略未知警告

#### 4.1 执行函数测试
- **测试执行方式**：使用 `uv run` 执行测试代码
- 直接执行生成的每个指标函数
- 使用合理的测试参数（如有效的股票代码和日期）
- 观察函数是否能正常运行

#### 4.2 错误修复
**如果出现错误**：
- 根据错误信息立刻进行修复
- 常见错误类型：导入错误、API调用错误、数据处理错误、计算错误、类型错误
- 修复后重新测试直到函数正常运行

#### 4.3 结果验证
**如果函数可以执行**：
- 仔细核对计算结果的合理性
- **特别注意**：如果出现0、1、NaN、inf等特殊值，必须核对并确定真实原因
- **可接受情况**：源数据缺失导致的特殊值
- **需要修复**：指标设计不合理或代码逻辑错误导致的特殊值
- 对于异常结果，分析原因并修复指标设计或代码逻辑

#### 4.4 迭代修复
- 持续修复直到无错误或实在无法解决
- 确保所有指标函数都能正常运行并产生合理结果

#### 注意事项

**网络问题处理**：
- 因为开发流程中已经完成了数据验证，因此在测试环节不得以网络问题为借口简化甚至跳过测试
- 如果遇到无法获取数据的情况，绝大部分情况是API调用参数不正确，少部分是等候时间不够长
- 必须仔细检查API调用参数的正确性和完整性，应优先使用示例中的股票代码和最近交易日
- 如果API响应缓慢，应适当增加等待时间而非跳过测试

### 最后一步：继续下一个API的指标设计
完成当前API的指标编程实现后，必须调用工具继续下一个API的指标设计流程：

```
generate_indicator_design_prompt_mcp()
```

此工具将自动选择下一个待处理的API并开始指标设计流程。

### 重要提醒
**必须再次调用工具**：完成当前API的编程实现后，必须调用generate_indicator_design_prompt_mcp继续下一个API的处理。任何情况下不能自行中止开发流程，必须通过工具调用来推进到下一个API的指标设计阶段。
